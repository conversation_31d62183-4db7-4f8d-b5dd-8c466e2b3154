"""
Core Agent Implementation

The main agent that orchestrates LLM providers, tool execution, and retry logic.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from ..config.settings import get_settings
from ..config.system_prompt import get_system_prompt
from ..utils.logger import get_logger
from .llm_providers import DeepSeekProvider, OllamaProvider
from .tool_executor import ToolExecutor
from .retry_logic import RetryManager


class ArienAgent:
    """
    Main agent class that coordinates all components of the system.
    
    Features:
    - Multi-provider LLM support (DeepSeek, Ollama)
    - Dynamic tool loading and execution
    - Intelligent retry logic with never-give-up approach
    - Conversation memory and context management
    - Tool output capture and processing
    """
    
    def __init__(
        self,
        provider: str = "deepseek",
        model: str = "deepseek-chat",
        max_retries: int = 3,
        verbose: bool = False
    ):
        self.logger = get_logger(__name__)
        self.settings = get_settings()
        self.verbose = verbose
        
        # Initialize components
        self.llm_provider = self._initialize_llm_provider(provider, model)
        self.tool_executor = ToolExecutor(verbose=verbose)
        self.retry_manager = RetryManager(max_retries=max_retries)
        
        # Agent state
        self.conversation_history: List[Dict[str, Any]] = []
        self.tool_results_cache: Dict[str, Any] = {}
        self.session_start_time = datetime.now()
        self.total_messages = 0
        self.successful_tool_calls = 0
        self.failed_tool_calls = 0
        
        self.logger.info(f"Arien Agent initialized with {provider} provider and {model} model")
    
    def _initialize_llm_provider(self, provider: str, model: str):
        """Initialize the specified LLM provider."""
        if provider.lower() == "deepseek":
            return DeepSeekProvider(model=model, verbose=self.verbose)
        elif provider.lower() == "ollama":
            return OllamaProvider(model=model, verbose=self.verbose)
        else:
            raise ValueError(f"Unsupported provider: {provider}")
    
    async def process_message(self, user_message: str) -> str:
        """
        Process a user message through the complete agent pipeline.
        
        Args:
            user_message: The user's input message
            
        Returns:
            The agent's response after processing and tool execution
        """
        self.total_messages += 1
        start_time = time.time()
        
        try:
            # Add user message to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })
            
            # Process with retry logic
            response = await self.retry_manager.execute_with_retry(
                self._process_with_tools,
                user_message
            )
            
            # Add assistant response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat(),
                "processing_time": time.time() - start_time
            })
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            error_response = f"I encountered an error while processing your request: {str(e)}"
            
            self.conversation_history.append({
                "role": "assistant",
                "content": error_response,
                "timestamp": datetime.now().isoformat(),
                "error": True
            })
            
            return error_response
    
    async def _process_with_tools(self, user_message: str) -> str:
        """
        Core processing logic with tool integration.
        
        This method implements the main agent loop:
        1. Get LLM response with available tools
        2. Execute any requested tools
        3. Continue conversation with tool results
        4. Return final response
        """
        # Prepare conversation context
        messages = self._prepare_conversation_context(user_message)
        
        # Get available tools
        available_tools = self.tool_executor.get_available_tools()
        
        # Initial LLM call
        response = await self.llm_provider.generate_response(
            messages=messages,
            tools=available_tools
        )
        
        # Check if tools were requested
        if self._has_tool_calls(response):
            return await self._handle_tool_calls(response, messages)
        else:
            return self._extract_content(response)
    
    def _prepare_conversation_context(self, user_message: str) -> List[Dict[str, Any]]:
        """Prepare the conversation context for the LLM."""
        messages = []
        
        # Add system prompt
        system_prompt = get_system_prompt()
        messages.append({
            "role": "system",
            "content": system_prompt.get_full_prompt()
        })
        
        # Add recent conversation history (last 10 exchanges)
        recent_history = self.conversation_history[-20:] if len(self.conversation_history) > 20 else self.conversation_history
        for msg in recent_history:
            if msg["role"] in ["user", "assistant"]:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        return messages
    
    def _has_tool_calls(self, response: Dict[str, Any]) -> bool:
        """Check if the LLM response contains tool calls."""
        return "tool_calls" in response and response["tool_calls"]
    
    async def _handle_tool_calls(self, response: Dict[str, Any], messages: List[Dict[str, Any]]) -> str:
        """
        Handle tool calls from the LLM response.
        
        This implements the tool execution loop with proper error handling
        and result integration back into the conversation.
        """
        tool_calls = response.get("tool_calls", [])
        tool_results = []
        
        # Execute each tool call
        for tool_call in tool_calls:
            try:
                result = await self.tool_executor.execute_tool(tool_call)
                tool_results.append(result)
                self.successful_tool_calls += 1
                
                # Cache result for potential reuse
                tool_id = tool_call.get("id", f"tool_{len(self.tool_results_cache)}")
                self.tool_results_cache[tool_id] = result
                
            except Exception as e:
                self.failed_tool_calls += 1
                error_result = {
                    "tool_call_id": tool_call.get("id"),
                    "error": str(e),
                    "success": False
                }
                tool_results.append(error_result)
                self.logger.error(f"Tool execution failed: {e}")
        
        # Add tool results to conversation and get final response
        messages.append({
            "role": "assistant",
            "content": response.get("content", ""),
            "tool_calls": tool_calls
        })
        
        # Add tool results
        for result in tool_results:
            messages.append({
                "role": "tool",
                "content": json.dumps(result, indent=2),
                "tool_call_id": result.get("tool_call_id")
            })
        
        # Get final response from LLM
        final_response = await self.llm_provider.generate_response(messages=messages)
        return self._extract_content(final_response)
    
    def _extract_content(self, response: Dict[str, Any]) -> str:
        """Extract content from LLM response."""
        if isinstance(response, dict):
            return response.get("content", str(response))
        return str(response)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status and statistics."""
        uptime = datetime.now() - self.session_start_time
        
        return {
            "Provider": self.llm_provider.__class__.__name__,
            "Model": getattr(self.llm_provider, 'model', 'Unknown'),
            "Uptime": str(uptime).split('.')[0],  # Remove microseconds
            "Total Messages": self.total_messages,
            "Conversation Length": len(self.conversation_history),
            "Successful Tool Calls": self.successful_tool_calls,
            "Failed Tool Calls": self.failed_tool_calls,
            "Tool Success Rate": f"{(self.successful_tool_calls / max(1, self.successful_tool_calls + self.failed_tool_calls)) * 100:.1f}%",
            "Available Tools": len(self.tool_executor.get_available_tools()),
            "Cached Results": len(self.tool_results_cache)
        }
    
    def clear_conversation(self):
        """Clear conversation history."""
        self.conversation_history.clear()
        self.logger.info("Conversation history cleared")
    
    def export_conversation(self) -> Dict[str, Any]:
        """Export conversation history for analysis or backup."""
        return {
            "session_info": {
                "start_time": self.session_start_time.isoformat(),
                "total_messages": self.total_messages,
                "provider": self.llm_provider.__class__.__name__,
                "model": getattr(self.llm_provider, 'model', 'Unknown')
            },
            "conversation": self.conversation_history,
            "statistics": self.get_status()
        }
