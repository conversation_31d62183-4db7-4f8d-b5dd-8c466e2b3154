"""
Code Tools

Code analysis, generation, and manipulation capabilities.
"""

import ast
import re
import json
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import subprocess

from .base_tool import BaseTool, ToolResult
from ..config.settings import get_settings
from ..utils.logger import get_logger
from ..utils.helpers import format_size


class CodeTools(BaseTool):
    """
    Code analysis and generation tools.
    
    Features:
    - Code parsing and AST analysis
    - Syntax validation
    - Code formatting and linting
    - Documentation generation
    - Code metrics and complexity analysis
    """
    
    def __init__(self, verbose: bool = False):
        super().__init__("code_tools", "Code analysis and generation", verbose)
        self.settings = get_settings()
        self.max_file_size = self.settings.code_tools_max_file_size
        self.supported_languages = self.settings.code_tools_supported_languages
    
    async def analyze_python_code(self, code: str, file_path: Optional[str] = None) -> ToolResult:
        """
        Analyze Python code structure and extract information.
        
        Args:
            code: Python code to analyze
            file_path: Optional file path for context
        """
        try:
            # Parse the code into AST
            tree = ast.parse(code, filename=file_path or '<string>')
            
            analysis = {
                "file_path": file_path,
                "total_lines": len(code.splitlines()),
                "classes": [],
                "functions": [],
                "imports": [],
                "variables": [],
                "complexity": {
                    "cyclomatic_complexity": 0,
                    "cognitive_complexity": 0
                },
                "metrics": {
                    "lines_of_code": len([line for line in code.splitlines() if line.strip()]),
                    "comment_lines": len([line for line in code.splitlines() if line.strip().startswith('#')]),
                    "blank_lines": len([line for line in code.splitlines() if not line.strip()])
                }
            }
            
            # Walk through AST nodes
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        "name": node.name,
                        "line_number": node.lineno,
                        "methods": [],
                        "decorators": [ast.unparse(d) for d in node.decorator_list],
                        "docstring": ast.get_docstring(node)
                    }
                    
                    # Get methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            class_info["methods"].append({
                                "name": item.name,
                                "line_number": item.lineno,
                                "args": [arg.arg for arg in item.args.args],
                                "decorators": [ast.unparse(d) for d in item.decorator_list],
                                "docstring": ast.get_docstring(item)
                            })
                    
                    analysis["classes"].append(class_info)
                
                elif isinstance(node, ast.FunctionDef):
                    # Only top-level functions (not methods)
                    if not any(isinstance(parent, ast.ClassDef) for parent in ast.walk(tree) 
                              if hasattr(parent, 'body') and node in getattr(parent, 'body', [])):
                        function_info = {
                            "name": node.name,
                            "line_number": node.lineno,
                            "args": [arg.arg for arg in node.args.args],
                            "decorators": [ast.unparse(d) for d in node.decorator_list],
                            "docstring": ast.get_docstring(node),
                            "returns": ast.unparse(node.returns) if node.returns else None
                        }
                        analysis["functions"].append(function_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis["imports"].append({
                                "type": "import",
                                "module": alias.name,
                                "alias": alias.asname,
                                "line_number": node.lineno
                            })
                    else:  # ImportFrom
                        for alias in node.names:
                            analysis["imports"].append({
                                "type": "from_import",
                                "module": node.module,
                                "name": alias.name,
                                "alias": alias.asname,
                                "line_number": node.lineno
                            })
                
                elif isinstance(node, ast.Assign):
                    # Global variable assignments
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            analysis["variables"].append({
                                "name": target.id,
                                "line_number": node.lineno,
                                "type": "assignment"
                            })
                
                # Calculate complexity
                if isinstance(node, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                    analysis["complexity"]["cyclomatic_complexity"] += 1
                elif isinstance(node, (ast.And, ast.Or)):
                    analysis["complexity"]["cognitive_complexity"] += 1
            
            return ToolResult(
                success=True,
                data=analysis,
                metadata={"language": "python", "analysis_type": "ast"}
            )
            
        except SyntaxError as e:
            return ToolResult(
                success=False,
                error=f"Python syntax error: {e.msg} at line {e.lineno}"
            )
        except Exception as e:
            return ToolResult(success=False, error=f"Error analyzing Python code: {str(e)}")
    
    async def validate_syntax(self, code: str, language: str) -> ToolResult:
        """
        Validate code syntax for supported languages.
        
        Args:
            code: Code to validate
            language: Programming language
        """
        try:
            language = language.lower()
            
            if language == "python":
                try:
                    ast.parse(code)
                    return ToolResult(
                        success=True,
                        data={"valid": True, "language": language},
                        metadata={"validation_method": "ast_parse"}
                    )
                except SyntaxError as e:
                    return ToolResult(
                        success=True,
                        data={
                            "valid": False,
                            "language": language,
                            "error": str(e),
                            "line": e.lineno,
                            "column": e.offset
                        },
                        metadata={"validation_method": "ast_parse"}
                    )
            
            elif language == "javascript":
                # Basic JavaScript syntax validation using regex patterns
                # This is a simplified check - for production use, consider using a proper JS parser
                
                # Check for basic syntax issues
                issues = []
                
                # Check for unmatched brackets
                brackets = {'(': ')', '[': ']', '{': '}'}
                stack = []
                for i, char in enumerate(code):
                    if char in brackets:
                        stack.append((char, i))
                    elif char in brackets.values():
                        if not stack:
                            issues.append(f"Unmatched closing bracket '{char}' at position {i}")
                        else:
                            open_bracket, _ = stack.pop()
                            if brackets[open_bracket] != char:
                                issues.append(f"Mismatched brackets at position {i}")
                
                if stack:
                    for bracket, pos in stack:
                        issues.append(f"Unmatched opening bracket '{bracket}' at position {pos}")
                
                # Check for basic syntax patterns
                if re.search(r'function\s+\w+\s*\([^)]*\)\s*{', code):
                    # Has function declarations
                    pass
                
                return ToolResult(
                    success=True,
                    data={
                        "valid": len(issues) == 0,
                        "language": language,
                        "issues": issues
                    },
                    metadata={"validation_method": "regex_patterns"}
                )
            
            elif language == "json":
                try:
                    json.loads(code)
                    return ToolResult(
                        success=True,
                        data={"valid": True, "language": language},
                        metadata={"validation_method": "json_loads"}
                    )
                except json.JSONDecodeError as e:
                    return ToolResult(
                        success=True,
                        data={
                            "valid": False,
                            "language": language,
                            "error": str(e),
                            "line": e.lineno,
                            "column": e.colno
                        },
                        metadata={"validation_method": "json_loads"}
                    )
            
            else:
                return ToolResult(
                    success=False,
                    error=f"Syntax validation not supported for language: {language}"
                )
                
        except Exception as e:
            return ToolResult(success=False, error=f"Error validating syntax: {str(e)}")
    
    async def format_code(self, code: str, language: str) -> ToolResult:
        """
        Format code using appropriate formatters.
        
        Args:
            code: Code to format
            language: Programming language
        """
        try:
            language = language.lower()
            
            if language == "python":
                try:
                    # Try to use black for formatting if available
                    result = subprocess.run(
                        ['black', '--code', code],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
                    
                    if result.returncode == 0:
                        return ToolResult(
                            success=True,
                            data={
                                "formatted_code": result.stdout,
                                "language": language,
                                "formatter": "black"
                            }
                        )
                    else:
                        return ToolResult(
                            success=False,
                            error=f"Black formatting failed: {result.stderr}"
                        )
                        
                except FileNotFoundError:
                    # Black not available, do basic formatting
                    lines = code.split('\n')
                    formatted_lines = []
                    indent_level = 0
                    
                    for line in lines:
                        stripped = line.strip()
                        if not stripped:
                            formatted_lines.append('')
                            continue
                        
                        # Decrease indent for certain keywords
                        if stripped.startswith(('except', 'elif', 'else', 'finally')):
                            current_indent = max(0, indent_level - 1)
                        elif stripped.startswith(('def ', 'class ', 'if ', 'for ', 'while ', 'try:')):
                            current_indent = indent_level
                        else:
                            current_indent = indent_level
                        
                        formatted_lines.append('    ' * current_indent + stripped)
                        
                        # Increase indent after certain keywords
                        if stripped.endswith(':'):
                            indent_level += 1
                        elif stripped in ('pass', 'break', 'continue', 'return'):
                            indent_level = max(0, indent_level - 1)
                    
                    return ToolResult(
                        success=True,
                        data={
                            "formatted_code": '\n'.join(formatted_lines),
                            "language": language,
                            "formatter": "basic"
                        }
                    )
            
            elif language == "json":
                try:
                    parsed = json.loads(code)
                    formatted = json.dumps(parsed, indent=2, ensure_ascii=False)
                    
                    return ToolResult(
                        success=True,
                        data={
                            "formatted_code": formatted,
                            "language": language,
                            "formatter": "json"
                        }
                    )
                except json.JSONDecodeError as e:
                    return ToolResult(
                        success=False,
                        error=f"Invalid JSON: {str(e)}"
                    )
            
            else:
                return ToolResult(
                    success=False,
                    error=f"Code formatting not supported for language: {language}"
                )
                
        except Exception as e:
            return ToolResult(success=False, error=f"Error formatting code: {str(e)}")
    
    async def generate_documentation(self, code: str, language: str) -> ToolResult:
        """
        Generate documentation for code.
        
        Args:
            code: Code to document
            language: Programming language
        """
        try:
            if language.lower() == "python":
                # Analyze the code first
                analysis_result = await self.analyze_python_code(code)
                if not analysis_result.success:
                    return analysis_result
                
                analysis = analysis_result.data
                
                # Generate documentation
                doc = {
                    "overview": {
                        "total_lines": analysis["total_lines"],
                        "classes": len(analysis["classes"]),
                        "functions": len(analysis["functions"]),
                        "imports": len(analysis["imports"])
                    },
                    "classes": [],
                    "functions": [],
                    "imports": analysis["imports"]
                }
                
                # Document classes
                for cls in analysis["classes"]:
                    class_doc = {
                        "name": cls["name"],
                        "line": cls["line_number"],
                        "description": cls["docstring"] or "No description available",
                        "methods": []
                    }
                    
                    for method in cls["methods"]:
                        method_doc = {
                            "name": method["name"],
                            "line": method["line_number"],
                            "parameters": method["args"],
                            "description": method["docstring"] or "No description available"
                        }
                        class_doc["methods"].append(method_doc)
                    
                    doc["classes"].append(class_doc)
                
                # Document functions
                for func in analysis["functions"]:
                    func_doc = {
                        "name": func["name"],
                        "line": func["line_number"],
                        "parameters": func["args"],
                        "returns": func["returns"],
                        "description": func["docstring"] or "No description available"
                    }
                    doc["functions"].append(func_doc)
                
                return ToolResult(
                    success=True,
                    data=doc,
                    metadata={"language": language, "doc_type": "generated"}
                )
            
            else:
                return ToolResult(
                    success=False,
                    error=f"Documentation generation not supported for language: {language}"
                )
                
        except Exception as e:
            return ToolResult(success=False, error=f"Error generating documentation: {str(e)}")
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute code operation based on method parameter."""
        method = kwargs.get('method', 'analyze_python_code')
        
        if method == 'analyze_python_code':
            return await self.analyze_python_code(
                kwargs.get('code'),
                kwargs.get('file_path')
            )
        elif method == 'validate_syntax':
            return await self.validate_syntax(
                kwargs.get('code'),
                kwargs.get('language')
            )
        elif method == 'format_code':
            return await self.format_code(
                kwargs.get('code'),
                kwargs.get('language')
            )
        elif method == 'generate_documentation':
            return await self.generate_documentation(
                kwargs.get('code'),
                kwargs.get('language')
            )
        else:
            return ToolResult(success=False, error=f"Unknown method: {method}")
    
    def get_schema(self) -> Dict[str, Any]:
        """Get schema for code tools."""
        return {
            "parameters": {
                "type": "object",
                "properties": {
                    "method": {
                        "type": "string",
                        "enum": ["analyze_python_code", "validate_syntax", "format_code", "generate_documentation"],
                        "description": "Code operation to perform"
                    },
                    "code": {
                        "type": "string",
                        "description": "Code to analyze/process"
                    },
                    "language": {
                        "type": "string",
                        "description": "Programming language"
                    },
                    "file_path": {
                        "type": "string",
                        "description": "Optional file path for context"
                    }
                },
                "required": ["method", "code"]
            }
        }
