#!/usr/bin/env python3
"""
Arien AI - Main CLI Entry Point

Powerful Local Agentic CLI Terminal System with LLM Function Tools
"""

import asyncio
import sys
import os
from typing import Optional, Dict, Any
from pathlib import Path

import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table

# Import core components
from .core.agent import ArienAgent
from .config.settings import get_settings
from .utils.logger import setup_logger, get_logger
from .ui.login import LoginComponent
from .ui.chat_input import ChatInputComponent
from .ui.spinner import create_spinner

# Initialize Typer app
app = typer.Typer(
    name="arien",
    help="Arien AI - Powerful Local Agentic CLI Terminal System",
    add_completion=False,
    rich_markup_mode="rich"
)

# Global console instance
console = Console()


def version_callback(value: bool):
    """Show version information."""
    if value:
        settings = get_settings()
        console.print(f"[bold cyan]Arien AI[/bold cyan] version [bold green]{settings.version}[/bold green]")
        raise typer.Exit()


@app.callback()
def main(
    version: Optional[bool] = typer.Option(
        None, "--version", "-v", callback=version_callback, is_eager=True,
        help="Show version and exit"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-V", help="Enable verbose output"
    ),
    debug: bool = typer.Option(
        False, "--debug", "-d", help="Enable debug mode"
    )
):
    """
    Arien AI - Powerful Local Agentic CLI Terminal System with LLM Function Tools
    
    A sophisticated CLI system with LLM function calling capabilities,
    intelligent retry logic, and comprehensive tool execution.
    """
    # Setup logging
    log_level = "DEBUG" if debug else ("INFO" if verbose else "WARNING")
    setup_logger(level=log_level, verbose=verbose)


@app.command()
def chat(
    message: Optional[str] = typer.Argument(None, help="Message to send to the agent"),
    provider: Optional[str] = typer.Option(
        None, "--provider", "-p", help="LLM provider (deepseek, ollama)"
    ),
    model: Optional[str] = typer.Option(
        None, "--model", "-m", help="Model to use"
    ),
    auto_login: bool = typer.Option(
        True, "--auto-login/--no-auto-login", help="Auto-login with existing config"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Enable verbose output"
    )
):
    """
    Start interactive chat or send a single message to the agent.
    
    Examples:
        arien chat                                    # Interactive mode
        arien chat "Hello, help me with Python"      # Single message
        arien chat --provider deepseek "Analyze code" # With specific provider
    """
    asyncio.run(_chat_command(message, provider, model, auto_login, verbose))


async def _chat_command(
    message: Optional[str],
    provider: Optional[str],
    model: Optional[str],
    auto_login: bool,
    verbose: bool
):
    """Async implementation of chat command."""
    logger = get_logger(__name__)
    
    try:
        # Display welcome banner
        _display_welcome_banner()
        
        # Login/Configuration phase
        login_component = LoginComponent(console)
        config = login_component.render(auto_login=auto_login)
        
        if not config:
            console.print("[bold red]Login failed. Exiting.[/bold red]")
            return
        
        # Override provider/model if specified
        if provider:
            config["provider"] = provider
        if model:
            config["model"] = model
        
        # Initialize agent
        with create_spinner("Initializing agent", "bold blue", console) as spinner:
            agent = ArienAgent(
                provider=config["provider"],
                model=config.get("model", "deepseek-chat"),
                verbose=verbose
            )
            spinner.stop()
        
        console.print("[bold green]✓[/bold green] Agent initialized successfully!")
        console.print()
        
        # Single message mode
        if message:
            await _process_single_message(agent, message)
            return
        
        # Interactive chat mode
        await _interactive_chat_mode(agent)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Chat interrupted by user.[/yellow]")
    except Exception as e:
        logger.error(f"Error in chat command: {e}")
        console.print(f"[bold red]Error: {e}[/bold red]")


async def _process_single_message(agent: ArienAgent, message: str):
    """Process a single message and exit."""
    console.print(f"[bold blue]User:[/bold blue] {message}")
    console.print()
    
    with create_spinner("Processing", "bold green", console) as spinner:
        response = await agent.process_message(message)
        spinner.stop()
    
    console.print(f"[bold cyan]Assistant:[/bold cyan] {response}")


async def _interactive_chat_mode(agent: ArienAgent):
    """Run interactive chat mode."""
    console.print("[bold yellow]🤖 Interactive Chat Mode[/bold yellow]")
    console.print("[dim]Type 'exit', 'quit', or 'bye' to exit. Use '/help' for commands.[/dim]")
    console.print()
    
    chat_input = ChatInputComponent(console)
    
    while True:
        try:
            # Get user input
            input_result = chat_input.render()
            
            if input_result["action"] == "exit":
                console.print("[yellow]Goodbye! 👋[/yellow]")
                break
            elif input_result["action"] == "interrupted":
                console.print("[yellow]Chat interrupted.[/yellow]")
                break
            elif input_result["action"] == "empty":
                continue
            elif input_result["action"] == "show_help":
                _display_help()
                continue
            elif input_result["action"] == "clear_screen":
                console.clear()
                continue
            elif input_result["action"] == "show_status":
                _display_status(agent)
                continue
            elif input_result["action"] == "switch_model":
                # Handle model switching
                new_model = input_result.get("model")
                provider = input_result.get("provider")
                console.print(f"[green]Switching to model: {new_model} (provider: {provider})[/green]")
                # TODO: Implement actual model switching in agent
                continue
            elif input_result["action"] == "switch_provider":
                # Handle provider switching
                new_provider = input_result.get("provider")
                new_model = input_result.get("model")
                console.print(f"[green]Switching to provider: {new_provider} with model: {new_model}[/green]")
                # TODO: Implement actual provider switching in agent
                continue
            elif input_result["action"] == "clear_history":
                # Handle clear history
                console.print("[green]Chat history cleared[/green]")
                # TODO: Implement actual history clearing in agent
                continue
            elif input_result["action"] == "show_config":
                # Handle show config
                _show_configuration()
                continue
            elif input_result["action"] == "set_config":
                # Handle set config
                key = input_result.get("key")
                value = input_result.get("value")
                console.print(f"[green]Setting {key} = {value}[/green]")
                # TODO: Implement actual config setting
                continue
            elif input_result["action"] == "cancelled":
                # Command was cancelled, continue
                continue
            elif input_result["action"] == "error":
                # Handle errors
                error = input_result.get("error", "Unknown error")
                console.print(f"[red]Error: {error}[/red]")
                continue
            elif input_result["action"] == "user_message":
                message = input_result["message"]
                
                # Process message
                console.print()
                with create_spinner("Processing", "bold green", console) as spinner:
                    response = await agent.process_message(message)
                    spinner.stop()
                
                console.print(f"[bold cyan]Assistant:[/bold cyan] {response}")
                console.print()
            
        except KeyboardInterrupt:
            console.print("\n[yellow]Use 'exit' to quit or press Ctrl+C again to force exit.[/yellow]")
            continue
        except Exception as e:
            console.print(f"[bold red]Error: {e}[/bold red]")
            continue


@app.command()
def config(
    show: bool = typer.Option(False, "--show", "-s", help="Show current configuration"),
    reset: bool = typer.Option(False, "--reset", "-r", help="Reset configuration"),
    provider: Optional[str] = typer.Option(None, "--provider", "-p", help="Set provider"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Set model")
):
    """
    Manage Arien AI configuration.
    """
    if show:
        _show_configuration()
    elif reset:
        _reset_configuration()
    elif provider or model:
        _update_configuration(provider, model)
    else:
        console.print("[yellow]Use --show, --reset, or specify --provider/--model[/yellow]")


def _display_welcome_banner():
    """Display the welcome banner."""
    banner_text = Text()
    banner_text.append("🚀 ", style="bold yellow")
    banner_text.append("Arien AI", style="bold cyan")
    banner_text.append(" - Powerful Local Agentic CLI Terminal System", style="bold white")
    
    panel = Panel(
        banner_text,
        border_style="cyan",
        padding=(1, 2),
        title="[bold cyan]Welcome[/bold cyan]",
        title_align="center",
    )
    console.print(panel)
    console.print()


def _display_help():
    """Display help information."""
    help_table = Table(title="Available Commands", show_header=True, header_style="bold magenta")
    help_table.add_column("Command", style="cyan", no_wrap=True)
    help_table.add_column("Description", style="white")
    
    commands = [
        ("exit, quit, bye", "Exit the chat"),
        ("help", "Show this help message"),
        ("clear", "Clear the screen"),
        ("status", "Show agent status"),
        ("/help", "Show slash commands"),
        ("Ctrl+C", "Interrupt current operation"),
    ]
    
    for cmd, desc in commands:
        help_table.add_row(cmd, desc)
    
    console.print(help_table)
    console.print()


def _display_status(agent: ArienAgent):
    """Display agent status."""
    status = agent.get_status()
    
    status_table = Table(title="Agent Status", show_header=True, header_style="bold green")
    status_table.add_column("Property", style="cyan", no_wrap=True)
    status_table.add_column("Value", style="white")
    
    for key, value in status.items():
        status_table.add_row(key, str(value))
    
    console.print(status_table)
    console.print()


def _show_configuration():
    """Show current configuration."""
    settings = get_settings()
    
    config_table = Table(title="Current Configuration", show_header=True, header_style="bold blue")
    config_table.add_column("Setting", style="cyan", no_wrap=True)
    config_table.add_column("Value", style="white")
    
    # Show relevant settings
    config_items = [
        ("App Name", settings.app_name),
        ("Version", settings.version),
        ("Debug Mode", settings.debug),
        ("Verbose Mode", settings.verbose),
        ("Default Provider", settings.default_provider),
        ("Default Model", settings.default_model),
        ("Max Retries", settings.max_retries),
        ("Log Level", settings.log_level),
    ]
    
    for key, value in config_items:
        config_table.add_row(key, str(value))
    
    console.print(config_table)


def _reset_configuration():
    """Reset configuration to defaults."""
    console.print("[yellow]Configuration reset functionality not implemented yet.[/yellow]")


def _update_configuration(provider: Optional[str], model: Optional[str]):
    """Update configuration with new values."""
    console.print("[yellow]Configuration update functionality not implemented yet.[/yellow]")


if __name__ == "__main__":
    app()
