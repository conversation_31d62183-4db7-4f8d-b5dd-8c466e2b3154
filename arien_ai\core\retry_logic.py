"""
Retry Logic System

Implements intelligent retry mechanisms with exponential backoff and never-give-up approach.
"""

import asyncio
import time
import random
from typing import Any, Callable, Optional, Dict, List
from dataclasses import dataclass
from enum import Enum

from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
    after_log
)

from ..utils.logger import get_logger


class RetryStrategy(Enum):
    """Different retry strategies available."""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    FIXED_DELAY = "fixed_delay"
    LINEAR_BACKOFF = "linear_backoff"
    NEVER_GIVE_UP = "never_give_up"


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    
    # Exception handling
    retry_on_exceptions: List[type] = None
    stop_on_exceptions: List[type] = None
    
    # Never give up mode
    never_give_up: bool = False
    max_never_give_up_time: float = 300.0  # 5 minutes max for never give up


class RetryManager:
    """
    Advanced retry manager with multiple strategies and intelligent error handling.
    
    Features:
    - Multiple retry strategies
    - Exponential backoff with jitter
    - Exception-specific retry logic
    - Never-give-up mode for critical operations
    - Detailed retry statistics and logging
    """
    
    def __init__(self, max_retries: int = 3, verbose: bool = False):
        self.max_retries = max_retries
        self.verbose = verbose
        self.logger = get_logger(__name__)
        
        # Statistics
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_retry_attempts = 0
        self.retry_history: List[Dict[str, Any]] = []
        
        # Default configuration
        self.default_config = RetryConfig(
            max_attempts=max_retries,
            retry_on_exceptions=[Exception],
            stop_on_exceptions=[KeyboardInterrupt, SystemExit]
        )
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        config: Optional[RetryConfig] = None,
        **kwargs
    ) -> Any:
        """
        Execute a function with retry logic.
        
        Args:
            func: Function to execute
            *args: Function arguments
            config: Retry configuration (uses default if None)
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: If all retry attempts fail
        """
        retry_config = config or self.default_config
        self.total_executions += 1
        
        execution_start = time.time()
        attempt = 0
        last_exception = None
        
        # Never give up mode
        if retry_config.never_give_up:
            return await self._execute_never_give_up(func, retry_config, *args, **kwargs)
        
        # Regular retry logic
        while attempt < retry_config.max_attempts:
            attempt += 1
            
            try:
                if self.verbose:
                    self.logger.info(f"Executing function (attempt {attempt}/{retry_config.max_attempts})")
                
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Success
                self.successful_executions += 1
                if attempt > 1:
                    self.total_retry_attempts += (attempt - 1)
                
                self._log_success(func.__name__, attempt, time.time() - execution_start)
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if we should stop on this exception
                if retry_config.stop_on_exceptions:
                    for stop_exc in retry_config.stop_on_exceptions:
                        if isinstance(e, stop_exc):
                            self.logger.error(f"Stopping retry due to {stop_exc.__name__}: {e}")
                            raise e
                
                # Check if we should retry on this exception
                should_retry = False
                if retry_config.retry_on_exceptions:
                    for retry_exc in retry_config.retry_on_exceptions:
                        if isinstance(e, retry_exc):
                            should_retry = True
                            break
                
                if not should_retry:
                    self.logger.error(f"Not retrying due to exception type: {type(e).__name__}")
                    raise e
                
                # Log retry attempt
                self._log_retry_attempt(func.__name__, attempt, retry_config.max_attempts, e)
                
                # If this was the last attempt, raise the exception
                if attempt >= retry_config.max_attempts:
                    break
                
                # Calculate delay and wait
                delay = self._calculate_delay(attempt, retry_config)
                if self.verbose:
                    self.logger.info(f"Waiting {delay:.2f}s before retry...")
                
                await asyncio.sleep(delay)
        
        # All attempts failed
        self.failed_executions += 1
        self.total_retry_attempts += (attempt - 1)
        
        self._log_failure(func.__name__, attempt, time.time() - execution_start, last_exception)
        raise last_exception
    
    async def _execute_never_give_up(
        self,
        func: Callable,
        config: RetryConfig,
        *args,
        **kwargs
    ) -> Any:
        """
        Execute with never-give-up strategy.
        
        This mode will keep retrying until success or max time is reached.
        """
        start_time = time.time()
        attempt = 0
        
        self.logger.info(f"Starting never-give-up execution (max time: {config.max_never_give_up_time}s)")
        
        while True:
            attempt += 1
            elapsed_time = time.time() - start_time
            
            # Check if we've exceeded max time
            if elapsed_time > config.max_never_give_up_time:
                self.logger.error(f"Never-give-up mode timed out after {elapsed_time:.2f}s")
                self.failed_executions += 1
                raise TimeoutError(f"Never-give-up mode timed out after {elapsed_time:.2f}s")
            
            try:
                if self.verbose:
                    self.logger.info(f"Never-give-up attempt {attempt} (elapsed: {elapsed_time:.2f}s)")
                
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Success
                self.successful_executions += 1
                self.total_retry_attempts += (attempt - 1)
                
                self.logger.info(f"Never-give-up succeeded after {attempt} attempts in {elapsed_time:.2f}s")
                return result
                
            except Exception as e:
                # Check for stop exceptions
                if config.stop_on_exceptions:
                    for stop_exc in config.stop_on_exceptions:
                        if isinstance(e, stop_exc):
                            self.logger.error(f"Never-give-up stopped due to {stop_exc.__name__}: {e}")
                            raise e
                
                self._log_retry_attempt(func.__name__, attempt, "∞", e)
                
                # Calculate delay with increasing backoff
                delay = self._calculate_delay(min(attempt, 10), config)  # Cap at attempt 10 for delay calculation
                
                if self.verbose:
                    self.logger.info(f"Never-give-up waiting {delay:.2f}s before retry...")
                
                await asyncio.sleep(delay)
    
    def _calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """Calculate delay based on retry strategy."""
        if config.strategy == RetryStrategy.FIXED_DELAY:
            delay = config.base_delay
            
        elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay * attempt
            
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.exponential_base ** (attempt - 1))
            
        else:  # Default to exponential backoff
            delay = config.base_delay * (config.exponential_base ** (attempt - 1))
        
        # Apply max delay limit
        delay = min(delay, config.max_delay)
        
        # Add jitter if enabled
        if config.jitter:
            jitter_amount = delay * 0.1  # 10% jitter
            delay += random.uniform(-jitter_amount, jitter_amount)
        
        return max(0, delay)
    
    def _log_retry_attempt(self, func_name: str, attempt: int, max_attempts: Any, exception: Exception):
        """Log retry attempt."""
        self.logger.warning(f"Retry {attempt}/{max_attempts} for {func_name}: {type(exception).__name__}: {exception}")
        
        # Add to retry history
        self.retry_history.append({
            "function": func_name,
            "attempt": attempt,
            "max_attempts": max_attempts,
            "exception": str(exception),
            "exception_type": type(exception).__name__,
            "timestamp": time.time()
        })
    
    def _log_success(self, func_name: str, attempts: int, total_time: float):
        """Log successful execution."""
        if attempts > 1:
            self.logger.info(f"Function {func_name} succeeded after {attempts} attempts in {total_time:.2f}s")
        elif self.verbose:
            self.logger.info(f"Function {func_name} succeeded on first attempt in {total_time:.2f}s")
    
    def _log_failure(self, func_name: str, attempts: int, total_time: float, exception: Exception):
        """Log failed execution."""
        self.logger.error(f"Function {func_name} failed after {attempts} attempts in {total_time:.2f}s: {exception}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get retry statistics."""
        success_rate = (self.successful_executions / max(1, self.total_executions)) * 100
        avg_retries = self.total_retry_attempts / max(1, self.total_executions)
        
        return {
            "total_executions": self.total_executions,
            "successful_executions": self.successful_executions,
            "failed_executions": self.failed_executions,
            "success_rate": f"{success_rate:.1f}%",
            "total_retry_attempts": self.total_retry_attempts,
            "average_retries_per_execution": f"{avg_retries:.2f}",
            "recent_failures": self.retry_history[-10:] if self.retry_history else []
        }
    
    def reset_statistics(self):
        """Reset all statistics."""
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_retry_attempts = 0
        self.retry_history.clear()
        
        if self.verbose:
            self.logger.info("Retry statistics reset")
    
    def create_config(
        self,
        max_attempts: int = 3,
        strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        never_give_up: bool = False,
        **kwargs
    ) -> RetryConfig:
        """Create a custom retry configuration."""
        return RetryConfig(
            max_attempts=max_attempts,
            strategy=strategy,
            base_delay=base_delay,
            max_delay=max_delay,
            never_give_up=never_give_up,
            **kwargs
        )
