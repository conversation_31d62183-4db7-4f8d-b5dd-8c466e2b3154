# Arien AI Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ARIEN_APP_NAME="Arien AI"
ARIEN_VERSION="1.0.0"
ARIEN_DEBUG=false
ARIEN_VERBOSE=false

# =============================================================================
# LLM PROVIDER SETTINGS
# =============================================================================

# DeepSeek Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_DEFAULT_MODEL=deepseek-chat
DEEPSEEK_MAX_TOKENS=4096
DEEPSEEK_TEMPERATURE=0.7

# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_DEFAULT_MODEL=llama3.2
OLLAMA_TIMEOUT=120

# =============================================================================
# TOOL SETTINGS
# =============================================================================

# General Tool Settings
ARIEN_TOOLS_ENABLED=true
ARIEN_TOOLS_TIMEOUT=30
ARIEN_TOOLS_MAX_OUTPUT_LENGTH=5000

# File Tool Settings
ARIEN_FILE_TOOLS_ENABLED=true
ARIEN_FILE_TOOLS_MAX_SIZE=10485760  # 10MB in bytes
ARIEN_FILE_TOOLS_ALLOWED_EXTENSIONS=.txt,.py,.js,.html,.css,.json,.xml,.yaml,.yml,.md,.rst,.log
ARIEN_FILE_TOOLS_BASE_PATH=  # Leave empty to use current directory

# Web Tool Settings
ARIEN_WEB_TOOLS_ENABLED=true
ARIEN_WEB_TOOLS_TIMEOUT=30
ARIEN_WEB_TOOLS_MAX_CONTENT_LENGTH=100000
ARIEN_WEB_TOOLS_USER_AGENT="Arien-AI/1.0 (Agentic CLI Terminal System)"

# System Tool Settings
ARIEN_SYSTEM_TOOLS_ENABLED=true
ARIEN_SYSTEM_TOOLS_ALLOW_DANGEROUS=false
ARIEN_SYSTEM_TOOLS_TIMEOUT=60

# Code Tool Settings
ARIEN_CODE_TOOLS_ENABLED=true
ARIEN_CODE_TOOLS_MAX_SIZE=1048576  # 1MB in bytes
ARIEN_CODE_TOOLS_SUPPORTED_LANGUAGES=python,javascript,typescript,java,cpp,c,rust,go,php,ruby,swift,kotlin

# =============================================================================
# RETRY SETTINGS
# =============================================================================
ARIEN_RETRY_MAX_ATTEMPTS=3
ARIEN_RETRY_BASE_DELAY=1.0
ARIEN_RETRY_MAX_DELAY=60.0
ARIEN_RETRY_EXPONENTIAL_BASE=2.0

# =============================================================================
# LOGGING SETTINGS
# =============================================================================
ARIEN_LOG_LEVEL=INFO
ARIEN_LOG_FILE=  # Leave empty for console only, or specify path like /path/to/arien.log
ARIEN_LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
ARIEN_LOG_MAX_SIZE=10485760  # 10MB
ARIEN_LOG_BACKUP_COUNT=5

# =============================================================================
# STORAGE SETTINGS
# =============================================================================
ARIEN_DATA_DIR=~/.arien-ai
ARIEN_CACHE_DIR=~/.arien-ai/cache
ARIEN_CONFIG_FILE=~/.arien-ai/config.json

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
ARIEN_API_KEY_ENCRYPTION=true
ARIEN_SECURE_MODE=false

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
ARIEN_MAX_CONCURRENT_TOOLS=5
ARIEN_MEMORY_LIMIT_MB=1024

# =============================================================================
# DEVELOPMENT SETTINGS (for development/testing only)
# =============================================================================
# ARIEN_DEV_MODE=false
# ARIEN_TEST_MODE=false
# ARIEN_MOCK_LLM=false
