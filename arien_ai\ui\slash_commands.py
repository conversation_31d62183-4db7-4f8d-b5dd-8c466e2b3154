"""
Slash Commands Component

Provides dropdown command selection interface for slash commands.
"""

import os
from typing import Optional, Dict, Any, List, Tuple
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.prompt import Prompt
from .base_component import BaseUIComponent
from ..core.llm_providers import DeepS<PERSON>P<PERSON><PERSON>, OllamaProvider


class SlashCommandsComponent(BaseUIComponent):
    """
    Slash commands component with dropdown selection interface.
    
    Features:
    - Command discovery and registration
    - Interactive dropdown selection
    - Dynamic parameter handling
    - ESC key navigation support
    """
    
    def __init__(self, console: Optional[Console] = None):
        super().__init__("slash_commands", console)
        
        # Register available commands
        self.commands = {
            "/model": {
                "description": "Switch LLM model",
                "parameters": ["model_name"],
                "handler": self._handle_model_command
            },
            "/provider": {
                "description": "Switch LLM provider",
                "parameters": ["provider_name"],
                "handler": self._handle_provider_command
            },
            "/clear": {
                "description": "Clear chat history",
                "parameters": [],
                "handler": self._handle_clear_command
            },
            "/status": {
                "description": "Show system status",
                "parameters": [],
                "handler": self._handle_status_command
            },
            "/help": {
                "description": "Show available commands",
                "parameters": [],
                "handler": self._handle_help_command
            },
            "/config": {
                "description": "Show/modify configuration",
                "parameters": ["key", "value"],
                "handler": self._handle_config_command
            },
            "/exit": {
                "description": "Exit the application",
                "parameters": [],
                "handler": self._handle_exit_command
            }
        }
        
        # Provider models mapping (will be dynamically updated)
        self.provider_models = {
            "deepseek": ["deepseek-chat", "deepseek-reasoner"],
            "ollama": ["llama3.2", "llama3.1", "codellama", "mistral", "qwen2.5"]
        }

        # Initialize providers for model listing
        self._providers = {}
    
    def render(self, input_text: str = "") -> Optional[Dict[str, Any]]:
        """
        Render slash commands interface.
        
        Args:
            input_text: Current input text (should start with "/")
            
        Returns:
            Command result dictionary or None if cancelled
        """
        if not input_text.startswith("/"):
            return None
        
        self.log_debug(f"Processing slash command input: {input_text}")
        
        # Parse input
        parts = input_text.strip().split()
        command = parts[0] if parts else "/"
        
        # Show command selection if just "/"
        if command == "/":
            return self._show_command_selection()
        
        # Handle specific command
        if command in self.commands:
            return self._handle_command(command, parts[1:])
        
        # Show filtered commands
        return self._show_filtered_commands(command)
    
    def _show_command_selection(self) -> Optional[Dict[str, Any]]:
        """Show all available commands for selection."""
        self.console.print("\n[bold cyan]Available Commands:[/bold cyan]")

        table = Table(show_header=True, header_style="bold magenta", box=None)
        table.add_column("Option", style="cyan", width=8)
        table.add_column("Command", style="cyan", width=12)
        table.add_column("Description", style="white")
        table.add_column("Parameters", style="dim")

        commands_list = list(self.commands.keys())
        for i, cmd in enumerate(commands_list, 1):
            info = self.commands[cmd]
            params = ", ".join(info["parameters"]) if info["parameters"] else "none"
            table.add_row(str(i), cmd, info["description"], params)

        self.console.print(table)
        self.console.print("\n[dim]Enter option number, command name, or press Ctrl+C to cancel[/dim]")

        # Get user selection
        try:
            selection = Prompt.ask(
                "[bold blue]Select command",
                default=""
            )

            if not selection.strip():
                return None

            # Handle numeric selection
            if selection.isdigit():
                index = int(selection) - 1
                if 0 <= index < len(commands_list):
                    selected_command = commands_list[index]
                    return self._handle_command(selected_command, [])
                else:
                    self.console.print("[yellow]Invalid option number[/yellow]")
                    return None

            # Handle command name selection
            if selection in commands_list:
                return self._handle_command(selection, [])

            # Handle partial matches
            matches = [cmd for cmd in commands_list if cmd.startswith(selection)]
            if len(matches) == 1:
                return self._handle_command(matches[0], [])
            elif len(matches) > 1:
                self.console.print(f"[yellow]Multiple matches found: {', '.join(matches)}[/yellow]")
                return None
            else:
                self.console.print(f"[yellow]No command found matching '{selection}'[/yellow]")
                return None

        except KeyboardInterrupt:
            self.console.print("\n[dim]Command selection cancelled[/dim]")
            return None
        except Exception as e:
            self.log_error(f"Error in command selection: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
            return None
    
    def _show_filtered_commands(self, partial_command: str) -> Optional[Dict[str, Any]]:
        """Show commands matching partial input."""
        matches = [cmd for cmd in self.commands.keys() if cmd.startswith(partial_command)]

        if not matches:
            self.console.print(f"[yellow]No commands found matching '{partial_command}'[/yellow]")
            return None

        if len(matches) == 1:
            return self._handle_command(matches[0], [])

        self.console.print(f"\n[bold cyan]Commands matching '{partial_command}':[/bold cyan]")

        table = Table(show_header=True, header_style="bold magenta", box=None)
        table.add_column("Option", style="cyan", width=8)
        table.add_column("Command", style="cyan")
        table.add_column("Description", style="white")

        for i, cmd in enumerate(matches, 1):
            table.add_row(str(i), cmd, self.commands[cmd]["description"])

        self.console.print(table)
        self.console.print("\n[dim]Enter option number, command name, or press Ctrl+C to cancel[/dim]")

        try:
            selection = Prompt.ask(
                "[bold blue]Select command",
                default=""
            )

            if not selection.strip():
                return None

            # Handle numeric selection
            if selection.isdigit():
                index = int(selection) - 1
                if 0 <= index < len(matches):
                    selected_command = matches[index]
                    return self._handle_command(selected_command, [])
                else:
                    self.console.print("[yellow]Invalid option number[/yellow]")
                    return None

            # Handle command name selection
            if selection in matches:
                return self._handle_command(selection, [])
            else:
                self.console.print(f"[yellow]Invalid selection '{selection}'[/yellow]")
                return None

        except KeyboardInterrupt:
            self.console.print("\n[dim]Command selection cancelled[/dim]")
            return None
        except Exception as e:
            self.log_error(f"Error in filtered command selection: {e}")
            self.console.print(f"[red]Error: {e}[/red]")
            return None
    
    def _handle_command(self, command: str, args: List[str]) -> Optional[Dict[str, Any]]:
        """Handle specific command execution."""
        if command not in self.commands:
            return None
        
        cmd_info = self.commands[command]
        handler = cmd_info["handler"]
        
        self.log_debug(f"Executing command: {command} with args: {args}")
        
        try:
            return handler(args)
        except Exception as e:
            self.log_error(f"Error executing command {command}: {e}")
            self.console.print(f"[bold red]Error executing command:[/bold red] {e}")
            return None
    
    def _get_available_models(self, provider: str) -> List[str]:
        """Get available models for a provider."""
        try:
            if provider == "deepseek":
                if "deepseek" not in self._providers:
                    self._providers["deepseek"] = DeepSeekProvider()
                return self._providers["deepseek"].list_models()
            elif provider == "ollama":
                if "ollama" not in self._providers:
                    self._providers["ollama"] = OllamaProvider()
                return self._providers["ollama"].list_models()
            else:
                return self.provider_models.get(provider, [])
        except Exception as e:
            self.log_warning(f"Failed to get models for {provider}: {e}")
            return self.provider_models.get(provider, [])

    def _handle_model_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle /model command."""
        current_provider = os.getenv("CURRENT_PROVIDER", "deepseek")
        available_models = self._get_available_models(current_provider)
        
        if args and args[0] in available_models:
            # Direct model selection
            selected_model = args[0]
        else:
            # Show model selection
            self.console.print(f"\n[bold cyan]Available models for {current_provider}:[/bold cyan]")
            
            table = Table(show_header=True, header_style="bold magenta", box=None)
            table.add_column("Option", style="cyan", width=8)
            table.add_column("Model", style="white")
            
            for i, model in enumerate(available_models, 1):
                table.add_row(str(i), model)
            
            self.console.print(table)
            
            try:
                choice = Prompt.ask(
                    "[bold blue]Select model",
                    choices=[str(i) for i in range(1, len(available_models) + 1)] + [""],
                    default="",
                    show_choices=False
                )
                
                if not choice:
                    return {"action": "cancelled"}
                
                selected_model = available_models[int(choice) - 1]
                
            except (KeyboardInterrupt, ValueError):
                return {"action": "cancelled"}
        
        return {
            "action": "switch_model",
            "model": selected_model,
            "provider": current_provider
        }
    
    def _handle_provider_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle /provider command."""
        providers = list(self.provider_models.keys())
        
        if args and args[0] in providers:
            selected_provider = args[0]
        else:
            self.console.print("\n[bold cyan]Available providers:[/bold cyan]")
            
            table = Table(show_header=True, header_style="bold magenta", box=None)
            table.add_column("Option", style="cyan", width=8)
            table.add_column("Provider", style="white")
            
            for i, provider in enumerate(providers, 1):
                table.add_row(str(i), provider)
            
            self.console.print(table)
            
            try:
                choice = Prompt.ask(
                    "[bold blue]Select provider",
                    choices=[str(i) for i in range(1, len(providers) + 1)] + [""],
                    default="",
                    show_choices=False
                )
                
                if not choice:
                    return {"action": "cancelled"}
                
                selected_provider = providers[int(choice) - 1]
                
            except (KeyboardInterrupt, ValueError):
                return {"action": "cancelled"}
        
        return {
            "action": "switch_provider",
            "provider": selected_provider,
            "model": self.provider_models[selected_provider][0]  # Default model
        }
    
    def _handle_clear_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle /clear command."""
        return {"action": "clear_history"}
    
    def _handle_status_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle /status command."""
        return {"action": "show_status"}
    
    def _handle_help_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle /help command."""
        return {"action": "show_help"}
    
    def _handle_config_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle /config command."""
        if len(args) >= 2:
            return {
                "action": "set_config",
                "key": args[0],
                "value": args[1]
            }
        else:
            return {"action": "show_config"}
    
    def _handle_exit_command(self, args: List[str]) -> Dict[str, Any]:
        """Handle /exit command."""
        return {"action": "exit"}
