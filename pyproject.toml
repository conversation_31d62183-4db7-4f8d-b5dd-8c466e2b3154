[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "arien-ai"
version = "1.0.0"
description = "Powerful Local Agentic CLI Terminal System with LLM Function Tools"
authors = [
    {name = "Arien AI", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
keywords = ["ai", "cli", "agent", "llm", "tools", "automation"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "typer[all]>=0.12.0",
    "rich>=13.7.0",
    "httpx>=0.27.0",
    "asyncio-mqtt>=0.16.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    "aiofiles>=23.2.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "requests>=2.31.0",
    "openai>=1.50.0",
    "ollama>=0.5.0",
    "psutil>=5.9.0",
    "pathlib>=1.0.0",
    "json-repair>=0.25.0",
    "tenacity>=8.2.0",
    "colorama>=0.4.6",
    "prompt-toolkit>=3.0.43",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[project.scripts]
arien = "arien_ai.main:app"
arien-ai = "arien_ai.main:app"

[project.urls]
Homepage = "https://github.com/arien-ai/arien-ai"
Repository = "https://github.com/arien-ai/arien-ai"
Documentation = "https://github.com/arien-ai/arien-ai/docs"
"Bug Tracker" = "https://github.com/arien-ai/arien-ai/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["arien_ai*"]

[tool.black]
line-length = 88
target-version = ['py311', 'py312', 'py313']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
