"""
System Tools

System administration and command execution capabilities.
"""

import asyncio
import subprocess
import platform
import psutil
import os
import shutil
from typing import Dict, List, Any, Optional, Union
from pathlib import Path

from .base_tool import BaseTool, ToolResult
from ..config.settings import get_settings
from ..utils.logger import get_logger
from ..utils.helpers import format_size, format_duration


class SystemTools(BaseTool):
    """
    System administration tools providing command execution and system monitoring.
    
    Features:
    - Command execution with safety checks
    - System monitoring (CPU, memory, disk)
    - Process management
    - Environment variable access
    - System information gathering
    """
    
    def __init__(self, verbose: bool = False):
        super().__init__("system_tools", "System administration and monitoring", verbose)
        self.settings = get_settings()
        self.timeout = self.settings.system_tools_timeout
        self.allow_dangerous = self.settings.system_tools_allow_dangerous
        
        # Dangerous commands that require explicit permission
        self.dangerous_commands = {
            'rm', 'del', 'rmdir', 'format', 'fdisk', 'mkfs',
            'dd', 'shutdown', 'reboot', 'halt', 'poweroff',
            'kill', 'killall', 'pkill', 'taskkill',
            'chmod', 'chown', 'passwd', 'su', 'sudo'
        }
    
    def _is_dangerous_command(self, command: str) -> bool:
        """Check if command is potentially dangerous."""
        cmd_parts = command.strip().split()
        if not cmd_parts:
            return False
        
        base_cmd = Path(cmd_parts[0]).name.lower()
        return base_cmd in self.dangerous_commands
    
    async def execute_command(
        self,
        command: str,
        shell: bool = True,
        capture_output: bool = True,
        working_directory: Optional[str] = None,
        environment: Optional[Dict[str, str]] = None
    ) -> ToolResult:
        """
        Execute system command.
        
        Args:
            command: Command to execute
            shell: Execute in shell
            capture_output: Capture stdout/stderr
            working_directory: Working directory for command
            environment: Environment variables
        """
        try:
            # Safety check for dangerous commands
            if self._is_dangerous_command(command) and not self.allow_dangerous:
                return ToolResult(
                    success=False,
                    error=f"Dangerous command blocked: {command}. Enable allow_dangerous to execute."
                )
            
            # Prepare environment
            env = os.environ.copy()
            if environment:
                env.update(environment)
            
            # Prepare working directory
            cwd = working_directory or os.getcwd()
            
            if self.verbose:
                self.logger.info(f"Executing command: {command}")
                self.logger.info(f"Working directory: {cwd}")
            
            # Execute command
            if shell:
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=subprocess.PIPE if capture_output else None,
                    stderr=subprocess.PIPE if capture_output else None,
                    cwd=cwd,
                    env=env
                )
            else:
                cmd_parts = command.split()
                process = await asyncio.create_subprocess_exec(
                    *cmd_parts,
                    stdout=subprocess.PIPE if capture_output else None,
                    stderr=subprocess.PIPE if capture_output else None,
                    cwd=cwd,
                    env=env
                )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return ToolResult(
                    success=False,
                    error=f"Command timed out after {self.timeout}s"
                )
            
            # Process results
            result_data = {
                "command": command,
                "return_code": process.returncode,
                "success": process.returncode == 0
            }
            
            if capture_output:
                result_data["stdout"] = stdout.decode('utf-8', errors='replace') if stdout else ""
                result_data["stderr"] = stderr.decode('utf-8', errors='replace') if stderr else ""
            
            metadata = {
                "execution_info": {
                    "shell": shell,
                    "working_directory": cwd,
                    "environment_vars": len(env),
                    "timeout": self.timeout
                }
            }
            
            return ToolResult(
                success=process.returncode == 0,
                data=result_data,
                metadata=metadata,
                error=result_data["stderr"] if process.returncode != 0 and capture_output else None
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error executing command: {str(e)}")
    
    async def get_system_info(self) -> ToolResult:
        """Get comprehensive system information."""
        try:
            # Basic system info
            system_info = {
                "platform": {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor(),
                    "architecture": platform.architecture(),
                    "python_version": platform.python_version()
                },
                "cpu": {
                    "physical_cores": psutil.cpu_count(logical=False),
                    "logical_cores": psutil.cpu_count(logical=True),
                    "current_frequency": psutil.cpu_freq().current if psutil.cpu_freq() else None,
                    "usage_percent": psutil.cpu_percent(interval=1)
                },
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "used": psutil.virtual_memory().used,
                    "percentage": psutil.virtual_memory().percent,
                    "total_formatted": format_size(psutil.virtual_memory().total),
                    "available_formatted": format_size(psutil.virtual_memory().available),
                    "used_formatted": format_size(psutil.virtual_memory().used)
                },
                "disk": {},
                "network": {},
                "boot_time": psutil.boot_time()
            }
            
            # Disk usage for all mounted disks
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    system_info["disk"][partition.device] = {
                        "mountpoint": partition.mountpoint,
                        "filesystem": partition.fstype,
                        "total": usage.total,
                        "used": usage.used,
                        "free": usage.free,
                        "percentage": (usage.used / usage.total) * 100,
                        "total_formatted": format_size(usage.total),
                        "used_formatted": format_size(usage.used),
                        "free_formatted": format_size(usage.free)
                    }
                except PermissionError:
                    # Skip inaccessible partitions
                    continue
            
            # Network interfaces
            for interface, addresses in psutil.net_if_addrs().items():
                system_info["network"][interface] = []
                for addr in addresses:
                    system_info["network"][interface].append({
                        "family": str(addr.family),
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    })
            
            return ToolResult(
                success=True,
                data=system_info,
                metadata={"collection_time": psutil.boot_time()}
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error getting system info: {str(e)}")
    
    async def list_processes(self, filter_name: Optional[str] = None) -> ToolResult:
        """
        List running processes.
        
        Args:
            filter_name: Filter processes by name
        """
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    proc_info = proc.info
                    
                    # Filter by name if specified
                    if filter_name and filter_name.lower() not in proc_info['name'].lower():
                        continue
                    
                    # Get additional info
                    try:
                        proc_info['memory_mb'] = proc.memory_info().rss / 1024 / 1024
                        proc_info['create_time'] = proc.create_time()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        proc_info['memory_mb'] = None
                        proc_info['create_time'] = None
                    
                    processes.append(proc_info)
                    
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # Skip processes we can't access
                    continue
            
            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            
            metadata = {
                "total_processes": len(processes),
                "filter_applied": filter_name is not None,
                "filter_name": filter_name
            }
            
            return ToolResult(
                success=True,
                data=processes,
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error listing processes: {str(e)}")
    
    async def get_environment_variables(self, filter_prefix: Optional[str] = None) -> ToolResult:
        """
        Get environment variables.
        
        Args:
            filter_prefix: Filter variables by prefix
        """
        try:
            env_vars = {}
            
            for key, value in os.environ.items():
                if filter_prefix and not key.startswith(filter_prefix):
                    continue
                
                # Mask sensitive variables
                if any(sensitive in key.lower() for sensitive in ['password', 'secret', 'key', 'token']):
                    env_vars[key] = "***MASKED***"
                else:
                    env_vars[key] = value
            
            metadata = {
                "total_variables": len(env_vars),
                "filter_applied": filter_prefix is not None,
                "filter_prefix": filter_prefix
            }
            
            return ToolResult(
                success=True,
                data=env_vars,
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error getting environment variables: {str(e)}")
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute system operation based on method parameter."""
        method = kwargs.get('method', 'execute_command')
        
        if method == 'execute_command':
            return await self.execute_command(
                kwargs.get('command'),
                kwargs.get('shell', True),
                kwargs.get('capture_output', True),
                kwargs.get('working_directory'),
                kwargs.get('environment')
            )
        elif method == 'get_system_info':
            return await self.get_system_info()
        elif method == 'list_processes':
            return await self.list_processes(kwargs.get('filter_name'))
        elif method == 'get_environment_variables':
            return await self.get_environment_variables(kwargs.get('filter_prefix'))
        else:
            return ToolResult(success=False, error=f"Unknown method: {method}")
    
    def get_schema(self) -> Dict[str, Any]:
        """Get schema for system tools."""
        return {
            "parameters": {
                "type": "object",
                "properties": {
                    "method": {
                        "type": "string",
                        "enum": ["execute_command", "get_system_info", "list_processes", "get_environment_variables"],
                        "description": "System operation to perform"
                    },
                    "command": {
                        "type": "string",
                        "description": "Command to execute (for execute_command)"
                    },
                    "shell": {
                        "type": "boolean",
                        "description": "Execute in shell",
                        "default": True
                    },
                    "capture_output": {
                        "type": "boolean",
                        "description": "Capture stdout/stderr",
                        "default": True
                    },
                    "working_directory": {
                        "type": "string",
                        "description": "Working directory for command"
                    },
                    "environment": {
                        "type": "object",
                        "description": "Environment variables"
                    },
                    "filter_name": {
                        "type": "string",
                        "description": "Filter processes by name"
                    },
                    "filter_prefix": {
                        "type": "string",
                        "description": "Filter environment variables by prefix"
                    }
                },
                "required": ["method"]
            }
        }
