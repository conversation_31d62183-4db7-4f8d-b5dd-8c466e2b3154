"""
Tool Executor

Manages and executes tools dynamically with proper error handling and result processing.
"""

import json
import importlib
import inspect
from typing import Dict, List, Any, Optional, Type
from pathlib import Path

from ..tools.base_tool import BaseTool, ToolResult
from ..tools.file_tools import FileTools
from ..tools.web_tools import WebTools
from ..tools.system_tools import SystemTools
from ..tools.code_tools import CodeTools
from ..utils.logger import get_logger


class ToolExecutor:
    """
    Manages and executes tools for the Arien AI system.
    
    Features:
    - Dynamic tool loading and registration
    - Tool execution with error handling
    - Result processing and formatting
    - Tool statistics and monitoring
    """
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.logger = get_logger(__name__)
        self.tools: Dict[str, BaseTool] = {}
        
        # Load built-in tools
        self._load_builtin_tools()
        
        if self.verbose:
            self.logger.info(f"ToolExecutor initialized with {len(self.tools)} tools")
    
    def _load_builtin_tools(self):
        """Load all built-in tools."""
        try:
            # Initialize built-in tool classes
            builtin_tools = [
                FileTools(verbose=self.verbose),
                WebTools(verbose=self.verbose),
                SystemTools(verbose=self.verbose),
                CodeTools(verbose=self.verbose)
            ]
            
            # Register each tool's methods
            for tool_class in builtin_tools:
                self._register_tool_methods(tool_class)
                
        except Exception as e:
            self.logger.error(f"Error loading built-in tools: {e}")
    
    def _register_tool_methods(self, tool_instance: Any):
        """Register all tool methods from a tool class instance."""
        try:
            # Get all methods that are tools (have get_schema method)
            for method_name in dir(tool_instance):
                if method_name.startswith('_'):
                    continue
                
                method = getattr(tool_instance, method_name)
                if not callable(method):
                    continue
                
                # Check if this is a tool method (has schema)
                if hasattr(method, 'get_schema'):
                    tool_name = f"{tool_instance.__class__.__name__.lower()}_{method_name}"
                    self.tools[tool_name] = ToolMethodWrapper(
                        method, tool_name, verbose=self.verbose
                    )
                    
                    if self.verbose:
                        self.logger.info(f"Registered tool: {tool_name}")
                        
        except Exception as e:
            self.logger.error(f"Error registering tool methods for {tool_instance.__class__.__name__}: {e}")
    
    def register_tool(self, tool: BaseTool):
        """Register a custom tool."""
        try:
            self.tools[tool.name] = tool
            if self.verbose:
                self.logger.info(f"Registered custom tool: {tool.name}")
        except Exception as e:
            self.logger.error(f"Error registering tool {tool.name}: {e}")
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools in OpenAI function format."""
        tools = []
        
        for tool_name, tool in self.tools.items():
            try:
                schema = tool.get_openai_function_schema()
                tools.append(schema)
            except Exception as e:
                self.logger.error(f"Error getting schema for tool {tool_name}: {e}")
        
        return tools
    
    async def execute_tool(self, tool_call: Dict[str, Any]) -> ToolResult:
        """
        Execute a tool based on the tool call from the LLM.
        
        Args:
            tool_call: Tool call information from LLM
            
        Returns:
            ToolResult: Execution result
        """
        try:
            # Extract tool information
            tool_id = tool_call.get("id")
            function_info = tool_call.get("function", {})
            tool_name = function_info.get("name")
            arguments_str = function_info.get("arguments", "{}")
            
            if not tool_name:
                return ToolResult(
                    success=False,
                    error="No tool name provided in tool call",
                    tool_call_id=tool_id
                )
            
            # Parse arguments
            try:
                arguments = json.loads(arguments_str) if isinstance(arguments_str, str) else arguments_str
            except json.JSONDecodeError as e:
                return ToolResult(
                    success=False,
                    error=f"Invalid JSON in tool arguments: {e}",
                    tool_call_id=tool_id
                )
            
            # Find and execute tool
            if tool_name not in self.tools:
                return ToolResult(
                    success=False,
                    error=f"Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}",
                    tool_call_id=tool_id
                )
            
            tool = self.tools[tool_name]
            
            if self.verbose:
                self.logger.info(f"Executing tool: {tool_name} with arguments: {arguments}")
            
            # Execute tool
            result = await tool.safe_execute(tool_call_id=tool_id, **arguments)
            
            # Process result for LLM consumption
            processed_result = self._process_tool_result(result)
            
            return processed_result
            
        except Exception as e:
            self.logger.error(f"Error executing tool call: {e}")
            return ToolResult(
                success=False,
                error=f"Tool execution error: {str(e)}",
                tool_call_id=tool_call.get("id")
            )
    
    def _process_tool_result(self, result: ToolResult) -> ToolResult:
        """
        Process tool result for optimal LLM consumption.
        
        This method ensures that tool outputs are properly formatted
        and don't overwhelm the LLM with unnecessary details.
        """
        try:
            # If result is successful, format the data appropriately
            if result.success and result.data is not None:
                # Truncate large outputs
                if isinstance(result.data, str) and len(result.data) > 5000:
                    result.data = result.data[:5000] + "\n... [Output truncated for brevity]"
                
                # Format complex data structures
                elif isinstance(result.data, (dict, list)):
                    # Convert to JSON string with reasonable formatting
                    try:
                        json_str = json.dumps(result.data, indent=2, default=str)
                        if len(json_str) > 5000:
                            json_str = json_str[:5000] + "\n... [Output truncated for brevity]"
                        result.data = json_str
                    except Exception:
                        result.data = str(result.data)[:5000]
            
            # Add execution summary to metadata
            if not result.metadata:
                result.metadata = {}
            
            result.metadata.update({
                "processed": True,
                "output_length": len(str(result.data)) if result.data else 0,
                "execution_successful": result.success
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing tool result: {e}")
            # Return original result if processing fails
            return result
    
    def get_tool_statistics(self) -> Dict[str, Any]:
        """Get statistics for all tools."""
        stats = {
            "total_tools": len(self.tools),
            "tools": {}
        }
        
        for tool_name, tool in self.tools.items():
            try:
                stats["tools"][tool_name] = tool.get_statistics()
            except Exception as e:
                self.logger.error(f"Error getting statistics for tool {tool_name}: {e}")
                stats["tools"][tool_name] = {"error": str(e)}
        
        return stats
    
    def reset_all_statistics(self):
        """Reset statistics for all tools."""
        for tool in self.tools.values():
            try:
                tool.reset_statistics()
            except Exception as e:
                self.logger.error(f"Error resetting statistics for tool {tool.name}: {e}")
        
        if self.verbose:
            self.logger.info("All tool statistics reset")


class ToolMethodWrapper(BaseTool):
    """
    Wrapper to make regular methods behave like BaseTool instances.
    
    This allows us to use existing tool methods with the BaseTool interface.
    """
    
    def __init__(self, method, name: str, verbose: bool = False):
        self.method = method
        description = getattr(method, '__doc__', f"Tool method: {name}") or f"Tool method: {name}"
        super().__init__(name, description.strip(), verbose)
    
    def get_schema(self) -> Dict[str, Any]:
        """Get schema from the wrapped method."""
        if hasattr(self.method, 'get_schema'):
            return self.method.get_schema()
        else:
            # Generate basic schema from method signature
            sig = inspect.signature(self.method)
            parameters = {
                "type": "object",
                "properties": {},
                "required": []
            }
            
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                
                param_info = {"type": "string"}  # Default type
                if param.annotation != inspect.Parameter.empty:
                    if param.annotation == int:
                        param_info["type"] = "integer"
                    elif param.annotation == float:
                        param_info["type"] = "number"
                    elif param.annotation == bool:
                        param_info["type"] = "boolean"
                
                parameters["properties"][param_name] = param_info
                
                if param.default == inspect.Parameter.empty:
                    parameters["required"].append(param_name)
            
            return {"parameters": parameters}
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the wrapped method."""
        try:
            # Call the method
            if inspect.iscoroutinefunction(self.method):
                result = await self.method(**kwargs)
            else:
                result = self.method(**kwargs)
            
            # If method returns ToolResult, use it directly
            if isinstance(result, ToolResult):
                return result
            
            # Otherwise, wrap the result
            return ToolResult(
                success=True,
                data=result,
                tool_name=self.name
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                error=str(e),
                tool_name=self.name
            )
