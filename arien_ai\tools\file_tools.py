"""
File Tools

Comprehensive file system operations for the Arien AI system.
"""

import os
import shutil
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import aiofiles
import mimetypes

from .base_tool import BaseTool, ToolResult
from ..config.settings import get_settings
from ..utils.logger import get_logger
from ..utils.helpers import format_size, safe_filename


class FileTools(BaseTool):
    """
    File system operations tool providing comprehensive file management capabilities.
    
    Features:
    - Read/write files with encoding detection
    - Directory operations and navigation
    - File metadata and analysis
    - Safe file operations with validation
    - Backup and recovery functionality
    """
    
    def __init__(self, verbose: bool = False):
        super().__init__("file_tools", "File system operations and management", verbose)
        self.settings = get_settings()
        self.base_path = Path(self.settings.file_tools_base_path) if self.settings.file_tools_base_path else Path.cwd()
        self.max_file_size = self.settings.file_tools_max_file_size
        self.allowed_extensions = self.settings.file_tools_allowed_extensions
    
    def _validate_path(self, path: str) -> tuple[bool, Optional[str], Optional[Path]]:
        """Validate and resolve file path."""
        try:
            file_path = Path(path)
            
            # Make absolute if relative
            if not file_path.is_absolute():
                file_path = self.base_path / file_path
            
            # Resolve to handle .. and . components
            file_path = file_path.resolve()
            
            # Security check: ensure path is within allowed base path
            try:
                file_path.relative_to(self.base_path.resolve())
            except ValueError:
                return False, f"Path outside allowed directory: {path}", None
            
            return True, None, file_path
            
        except Exception as e:
            return False, f"Invalid path: {str(e)}", None
    
    def _check_file_size(self, file_path: Path) -> tuple[bool, Optional[str]]:
        """Check if file size is within limits."""
        try:
            if file_path.exists() and file_path.is_file():
                size = file_path.stat().st_size
                if size > self.max_file_size:
                    return False, f"File too large: {format_size(size)} > {format_size(self.max_file_size)}"
            return True, None
        except Exception as e:
            return False, f"Error checking file size: {str(e)}"
    
    def _check_extension(self, file_path: Path) -> tuple[bool, Optional[str]]:
        """Check if file extension is allowed."""
        if not self.allowed_extensions:
            return True, None
        
        extension = file_path.suffix.lower()
        if extension not in self.allowed_extensions:
            return False, f"File extension not allowed: {extension}. Allowed: {self.allowed_extensions}"
        
        return True, None
    
    async def read_file(self, path: str, encoding: str = "utf-8") -> ToolResult:
        """
        Read file content.
        
        Args:
            path: File path to read
            encoding: Text encoding (default: utf-8)
        """
        try:
            # Validate path
            valid, error, file_path = self._validate_path(path)
            if not valid:
                return ToolResult(success=False, error=error)
            
            # Check if file exists
            if not file_path.exists():
                return ToolResult(success=False, error=f"File not found: {path}")
            
            if not file_path.is_file():
                return ToolResult(success=False, error=f"Path is not a file: {path}")
            
            # Check file size
            valid_size, size_error = self._check_file_size(file_path)
            if not valid_size:
                return ToolResult(success=False, error=size_error)
            
            # Check extension
            valid_ext, ext_error = self._check_extension(file_path)
            if not valid_ext:
                return ToolResult(success=False, error=ext_error)
            
            # Read file
            async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                content = await f.read()
            
            # Get file metadata
            stat = file_path.stat()
            metadata = {
                "path": str(file_path),
                "size": stat.st_size,
                "size_formatted": format_size(stat.st_size),
                "modified": stat.st_mtime,
                "encoding": encoding,
                "lines": len(content.splitlines()),
                "characters": len(content)
            }
            
            return ToolResult(
                success=True,
                data=content,
                metadata=metadata
            )
            
        except UnicodeDecodeError as e:
            return ToolResult(success=False, error=f"Encoding error: {str(e)}. Try different encoding.")
        except Exception as e:
            return ToolResult(success=False, error=f"Error reading file: {str(e)}")
    
    def get_schema(self) -> Dict[str, Any]:
        """Get schema for read_file method."""
        return {
            "parameters": {
                "type": "object",
                "properties": {
                    "path": {
                        "type": "string",
                        "description": "Path to the file to read"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "Text encoding (default: utf-8)",
                        "default": "utf-8"
                    }
                },
                "required": ["path"]
            }
        }
    
    async def write_file(self, path: str, content: str, encoding: str = "utf-8", create_dirs: bool = True) -> ToolResult:
        """
        Write content to file.
        
        Args:
            path: File path to write
            content: Content to write
            encoding: Text encoding (default: utf-8)
            create_dirs: Create parent directories if they don't exist
        """
        try:
            # Validate path
            valid, error, file_path = self._validate_path(path)
            if not valid:
                return ToolResult(success=False, error=error)
            
            # Check extension
            valid_ext, ext_error = self._check_extension(file_path)
            if not valid_ext:
                return ToolResult(success=False, error=ext_error)
            
            # Create parent directories if needed
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Check if parent directory exists
            if not file_path.parent.exists():
                return ToolResult(success=False, error=f"Parent directory does not exist: {file_path.parent}")
            
            # Write file
            async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
                await f.write(content)
            
            # Get file metadata
            stat = file_path.stat()
            metadata = {
                "path": str(file_path),
                "size": stat.st_size,
                "size_formatted": format_size(stat.st_size),
                "encoding": encoding,
                "lines": len(content.splitlines()),
                "characters": len(content),
                "created": not file_path.existed_before if hasattr(file_path, 'existed_before') else True
            }
            
            return ToolResult(
                success=True,
                data=f"File written successfully: {file_path}",
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error writing file: {str(e)}")
    
    async def list_directory(self, path: str = ".", pattern: str = "*", include_hidden: bool = False) -> ToolResult:
        """
        List directory contents.
        
        Args:
            path: Directory path to list
            pattern: File pattern to match (default: *)
            include_hidden: Include hidden files/directories
        """
        try:
            # Validate path
            valid, error, dir_path = self._validate_path(path)
            if not valid:
                return ToolResult(success=False, error=error)
            
            # Check if directory exists
            if not dir_path.exists():
                return ToolResult(success=False, error=f"Directory not found: {path}")
            
            if not dir_path.is_dir():
                return ToolResult(success=False, error=f"Path is not a directory: {path}")
            
            # List contents
            items = []
            for item in dir_path.glob(pattern):
                # Skip hidden files if not requested
                if not include_hidden and item.name.startswith('.'):
                    continue
                
                try:
                    stat = item.stat()
                    item_info = {
                        "name": item.name,
                        "path": str(item),
                        "type": "directory" if item.is_dir() else "file",
                        "size": stat.st_size if item.is_file() else None,
                        "size_formatted": format_size(stat.st_size) if item.is_file() else None,
                        "modified": stat.st_mtime,
                        "permissions": oct(stat.st_mode)[-3:],
                        "extension": item.suffix if item.is_file() else None
                    }
                    
                    # Add MIME type for files
                    if item.is_file():
                        mime_type, _ = mimetypes.guess_type(str(item))
                        item_info["mime_type"] = mime_type
                    
                    items.append(item_info)
                    
                except Exception as e:
                    # Skip items we can't access
                    self.logger.warning(f"Could not access {item}: {e}")
                    continue
            
            # Sort items: directories first, then files
            items.sort(key=lambda x: (x["type"] != "directory", x["name"].lower()))
            
            metadata = {
                "directory": str(dir_path),
                "total_items": len(items),
                "files": len([i for i in items if i["type"] == "file"]),
                "directories": len([i for i in items if i["type"] == "directory"]),
                "pattern": pattern,
                "include_hidden": include_hidden
            }
            
            return ToolResult(
                success=True,
                data=items,
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error listing directory: {str(e)}")
    
    async def create_directory(self, path: str, parents: bool = True) -> ToolResult:
        """
        Create directory.
        
        Args:
            path: Directory path to create
            parents: Create parent directories if they don't exist
        """
        try:
            # Validate path
            valid, error, dir_path = self._validate_path(path)
            if not valid:
                return ToolResult(success=False, error=error)
            
            # Check if already exists
            if dir_path.exists():
                if dir_path.is_dir():
                    return ToolResult(
                        success=True,
                        data=f"Directory already exists: {dir_path}",
                        metadata={"path": str(dir_path), "already_existed": True}
                    )
                else:
                    return ToolResult(success=False, error=f"Path exists but is not a directory: {path}")
            
            # Create directory
            dir_path.mkdir(parents=parents, exist_ok=True)
            
            metadata = {
                "path": str(dir_path),
                "parents_created": parents,
                "already_existed": False
            }
            
            return ToolResult(
                success=True,
                data=f"Directory created successfully: {dir_path}",
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error creating directory: {str(e)}")
    
    async def delete_file(self, path: str, force: bool = False) -> ToolResult:
        """
        Delete file or directory.
        
        Args:
            path: Path to delete
            force: Force deletion without confirmation
        """
        try:
            # Validate path
            valid, error, file_path = self._validate_path(path)
            if not valid:
                return ToolResult(success=False, error=error)
            
            # Check if exists
            if not file_path.exists():
                return ToolResult(success=False, error=f"Path not found: {path}")
            
            # Safety check for important directories
            if not force and file_path.is_dir():
                important_dirs = {'.git', 'node_modules', '__pycache__', '.venv', 'venv'}
                if file_path.name in important_dirs:
                    return ToolResult(
                        success=False,
                        error=f"Refusing to delete important directory '{file_path.name}' without force=True"
                    )
            
            # Get info before deletion
            is_dir = file_path.is_dir()
            size = file_path.stat().st_size if file_path.is_file() else None
            
            # Delete
            if is_dir:
                shutil.rmtree(file_path)
            else:
                file_path.unlink()
            
            metadata = {
                "path": str(file_path),
                "type": "directory" if is_dir else "file",
                "size": size,
                "force": force
            }
            
            return ToolResult(
                success=True,
                data=f"{'Directory' if is_dir else 'File'} deleted successfully: {file_path}",
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error deleting: {str(e)}")
    
    async def copy_file(self, source: str, destination: str, overwrite: bool = False) -> ToolResult:
        """
        Copy file or directory.
        
        Args:
            source: Source path
            destination: Destination path
            overwrite: Overwrite if destination exists
        """
        try:
            # Validate paths
            valid_src, error_src, src_path = self._validate_path(source)
            if not valid_src:
                return ToolResult(success=False, error=f"Source: {error_src}")
            
            valid_dst, error_dst, dst_path = self._validate_path(destination)
            if not valid_dst:
                return ToolResult(success=False, error=f"Destination: {error_dst}")
            
            # Check source exists
            if not src_path.exists():
                return ToolResult(success=False, error=f"Source not found: {source}")
            
            # Check destination
            if dst_path.exists() and not overwrite:
                return ToolResult(success=False, error=f"Destination exists (use overwrite=True): {destination}")
            
            # Create destination parent directory
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy
            if src_path.is_dir():
                if dst_path.exists():
                    shutil.rmtree(dst_path)
                shutil.copytree(src_path, dst_path)
            else:
                shutil.copy2(src_path, dst_path)
            
            # Get metadata
            dst_stat = dst_path.stat()
            metadata = {
                "source": str(src_path),
                "destination": str(dst_path),
                "type": "directory" if dst_path.is_dir() else "file",
                "size": dst_stat.st_size if dst_path.is_file() else None,
                "overwrite": overwrite
            }
            
            return ToolResult(
                success=True,
                data=f"{'Directory' if dst_path.is_dir() else 'File'} copied successfully: {src_path} -> {dst_path}",
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error copying: {str(e)}")
    
    async def get_file_info(self, path: str) -> ToolResult:
        """
        Get detailed file/directory information.
        
        Args:
            path: Path to analyze
        """
        try:
            # Validate path
            valid, error, file_path = self._validate_path(path)
            if not valid:
                return ToolResult(success=False, error=error)
            
            # Check if exists
            if not file_path.exists():
                return ToolResult(success=False, error=f"Path not found: {path}")
            
            # Get basic info
            stat = file_path.stat()
            info = {
                "path": str(file_path),
                "name": file_path.name,
                "type": "directory" if file_path.is_dir() else "file",
                "size": stat.st_size,
                "size_formatted": format_size(stat.st_size),
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "accessed": stat.st_atime,
                "permissions": oct(stat.st_mode)[-3:],
                "owner_readable": os.access(file_path, os.R_OK),
                "owner_writable": os.access(file_path, os.W_OK),
                "owner_executable": os.access(file_path, os.X_OK)
            }
            
            # Add file-specific info
            if file_path.is_file():
                info["extension"] = file_path.suffix
                mime_type, encoding = mimetypes.guess_type(str(file_path))
                info["mime_type"] = mime_type
                info["encoding"] = encoding
                
                # Try to detect text encoding
                try:
                    with open(file_path, 'rb') as f:
                        sample = f.read(1024)
                    try:
                        sample.decode('utf-8')
                        info["text_encoding"] = "utf-8"
                        info["is_text"] = True
                    except UnicodeDecodeError:
                        info["is_text"] = False
                except:
                    info["is_text"] = None
            
            # Add directory-specific info
            elif file_path.is_dir():
                try:
                    contents = list(file_path.iterdir())
                    info["item_count"] = len(contents)
                    info["subdirectories"] = len([p for p in contents if p.is_dir()])
                    info["files"] = len([p for p in contents if p.is_file()])
                except PermissionError:
                    info["item_count"] = "Permission denied"
            
            return ToolResult(
                success=True,
                data=info,
                metadata={"analyzed_path": str(file_path)}
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error getting file info: {str(e)}")
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute file operation based on method parameter."""
        method = kwargs.get('method', 'read_file')
        
        if method == 'read_file':
            return await self.read_file(kwargs.get('path'), kwargs.get('encoding', 'utf-8'))
        elif method == 'write_file':
            return await self.write_file(
                kwargs.get('path'),
                kwargs.get('content'),
                kwargs.get('encoding', 'utf-8'),
                kwargs.get('create_dirs', True)
            )
        elif method == 'list_directory':
            return await self.list_directory(
                kwargs.get('path', '.'),
                kwargs.get('pattern', '*'),
                kwargs.get('include_hidden', False)
            )
        elif method == 'create_directory':
            return await self.create_directory(kwargs.get('path'), kwargs.get('parents', True))
        elif method == 'delete_file':
            return await self.delete_file(kwargs.get('path'), kwargs.get('force', False))
        elif method == 'copy_file':
            return await self.copy_file(
                kwargs.get('source'),
                kwargs.get('destination'),
                kwargs.get('overwrite', False)
            )
        elif method == 'get_file_info':
            return await self.get_file_info(kwargs.get('path'))
        else:
            return ToolResult(success=False, error=f"Unknown method: {method}")
    
    def get_schema(self) -> Dict[str, Any]:
        """Get schema for file tools."""
        return {
            "parameters": {
                "type": "object",
                "properties": {
                    "method": {
                        "type": "string",
                        "enum": ["read_file", "write_file", "list_directory", "create_directory", "delete_file", "copy_file", "get_file_info"],
                        "description": "File operation to perform"
                    },
                    "path": {
                        "type": "string",
                        "description": "File or directory path"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content to write (for write_file)"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "Text encoding (default: utf-8)",
                        "default": "utf-8"
                    },
                    "pattern": {
                        "type": "string",
                        "description": "File pattern for listing (default: *)",
                        "default": "*"
                    },
                    "include_hidden": {
                        "type": "boolean",
                        "description": "Include hidden files in listing",
                        "default": False
                    },
                    "create_dirs": {
                        "type": "boolean",
                        "description": "Create parent directories",
                        "default": True
                    },
                    "parents": {
                        "type": "boolean",
                        "description": "Create parent directories",
                        "default": True
                    },
                    "force": {
                        "type": "boolean",
                        "description": "Force operation without confirmation",
                        "default": False
                    },
                    "source": {
                        "type": "string",
                        "description": "Source path for copy operation"
                    },
                    "destination": {
                        "type": "string",
                        "description": "Destination path for copy operation"
                    },
                    "overwrite": {
                        "type": "boolean",
                        "description": "Overwrite existing files",
                        "default": False
                    }
                },
                "required": ["method"]
            }
        }
