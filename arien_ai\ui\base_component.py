"""
Base UI Component

Provides the base class for all UI components in the Arien AI CLI interface.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from rich.console import Console
from ..config.settings import get_settings
from ..utils.logger import get_logger


class BaseUIComponent(ABC):
    """
    Base class for all UI components.
    
    Provides common functionality like console access, settings, and logging.
    """
    
    def __init__(self, name: str, console: Optional[Console] = None):
        """
        Initialize the base UI component.
        
        Args:
            name: Component name for logging
            console: Rich console instance (creates new if None)
        """
        self.name = name
        self.console = console or Console()
        self.settings = get_settings()
        self.logger = get_logger(f"ui.{name}")
        self._state: Dict[str, Any] = {}
        
    @abstractmethod
    def render(self, **kwargs) -> Any:
        """
        Render the component.
        
        Args:
            **kwargs: Component-specific rendering arguments
            
        Returns:
            Component-specific return value
        """
        pass
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """Get component state value."""
        return self._state.get(key, default)
    
    def set_state(self, key: str, value: Any) -> None:
        """Set component state value."""
        self._state[key] = value
        
    def clear_state(self) -> None:
        """Clear all component state."""
        self._state.clear()
        
    def log_debug(self, message: str) -> None:
        """Log debug message."""
        self.logger.debug(f"[{self.name}] {message}")
        
    def log_info(self, message: str) -> None:
        """Log info message."""
        self.logger.info(f"[{self.name}] {message}")
        
    def log_warning(self, message: str) -> None:
        """Log warning message."""
        self.logger.warning(f"[{self.name}] {message}")
        
    def log_error(self, message: str) -> None:
        """Log error message."""
        self.logger.error(f"[{self.name}] {message}")
