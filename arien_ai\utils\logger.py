"""
Logging Utilities

Provides comprehensive logging functionality for the Arien AI system.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from rich.logging import RichHandler
from rich.console import Console

# Global logger registry
_loggers: Dict[str, logging.Logger] = {}
_console: Optional[Console] = None
_log_setup_done = False


def setup_logger(
    name: Optional[str] = None,
    level: str = "INFO",
    log_file: Optional[str] = None,
    verbose: bool = False,
    rich_console: bool = True
) -> logging.Logger:
    """
    Setup and configure a logger with both console and file output.
    
    Args:
        name: Logger name (defaults to root logger)
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional file path for log output
        verbose: Enable verbose logging
        rich_console: Use Rich console handler for colored output
        
    Returns:
        Configured logger instance
    """
    global _log_setup_done, _console
    
    # Determine log level
    if verbose:
        level = "DEBUG"
    
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Get or create logger
    logger_name = name or "arien_ai"
    logger = logging.getLogger(logger_name)
    
    # Avoid duplicate setup
    if logger_name in _loggers and _log_setup_done:
        return _loggers[logger_name]
    
    # Clear existing handlers
    logger.handlers.clear()
    logger.setLevel(log_level)
    
    # Create console handler
    if rich_console:
        if _console is None:
            _console = Console(stderr=True)
        
        console_handler = RichHandler(
            console=_console,
            show_time=True,
            show_path=verbose,
            rich_tracebacks=True,
            tracebacks_show_locals=verbose
        )
        console_format = "%(message)s"
    else:
        console_handler = logging.StreamHandler(sys.stderr)
        console_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    console_handler.setLevel(log_level)
    console_formatter = logging.Formatter(console_format)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Create file handler if specified
    if log_file:
        try:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Use rotating file handler to prevent huge log files
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            
            file_handler.setLevel(logging.DEBUG)  # File gets all messages
            file_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
        except Exception as e:
            logger.warning(f"Failed to setup file logging: {e}")
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    # Store logger
    _loggers[logger_name] = logger
    _log_setup_done = True
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for the specified name.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    if name in _loggers:
        return _loggers[name]
    
    # Create child logger
    parent_name = "arien_ai"
    if parent_name not in _loggers:
        setup_logger(parent_name)
    
    logger = logging.getLogger(name)
    _loggers[name] = logger
    
    return logger


def set_log_level(level: str, logger_name: Optional[str] = None):
    """
    Set log level for a specific logger or all loggers.
    
    Args:
        level: New log level
        logger_name: Specific logger name (None for all)
    """
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    if logger_name:
        if logger_name in _loggers:
            _loggers[logger_name].setLevel(log_level)
            for handler in _loggers[logger_name].handlers:
                if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                    handler.setLevel(log_level)
    else:
        # Set level for all loggers
        for logger in _loggers.values():
            logger.setLevel(log_level)
            for handler in logger.handlers:
                if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                    handler.setLevel(log_level)


def add_file_handler(log_file: str, logger_name: Optional[str] = None):
    """
    Add file handler to existing logger(s).
    
    Args:
        log_file: Path to log file
        logger_name: Specific logger name (None for all)
    """
    try:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        )
        file_handler.setFormatter(file_formatter)
        
        if logger_name:
            if logger_name in _loggers:
                _loggers[logger_name].addHandler(file_handler)
        else:
            for logger in _loggers.values():
                logger.addHandler(file_handler)
                
    except Exception as e:
        print(f"Failed to add file handler: {e}")


def log_function_call(func):
    """
    Decorator to log function calls with parameters and results.
    
    Usage:
        @log_function_call
        def my_function(arg1, arg2):
            return result
    """
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        func_name = func.__name__
        
        # Log function entry
        logger.debug(f"Calling {func_name} with args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func_name} returned: {type(result).__name__}")
            return result
        except Exception as e:
            logger.error(f"{func_name} raised {type(e).__name__}: {e}")
            raise
    
    return wrapper


def log_execution_time(func):
    """
    Decorator to log function execution time.
    
    Usage:
        @log_execution_time
        def my_function():
            # do something
            pass
    """
    import time
    
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        func_name = func.__name__
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func_name} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func_name} failed after {execution_time:.3f}s: {e}")
            raise
    
    return wrapper


class LogContext:
    """
    Context manager for temporary logging configuration.
    
    Usage:
        with LogContext(level="DEBUG", logger_name="my_logger"):
            # Temporary debug logging
            pass
    """
    
    def __init__(self, level: Optional[str] = None, logger_name: Optional[str] = None):
        self.level = level
        self.logger_name = logger_name
        self.original_levels = {}
    
    def __enter__(self):
        if self.level:
            # Store original levels
            if self.logger_name:
                if self.logger_name in _loggers:
                    self.original_levels[self.logger_name] = _loggers[self.logger_name].level
            else:
                for name, logger in _loggers.items():
                    self.original_levels[name] = logger.level
            
            # Set new level
            set_log_level(self.level, self.logger_name)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore original levels
        for name, level in self.original_levels.items():
            if name in _loggers:
                _loggers[name].setLevel(level)


def get_log_statistics() -> Dict[str, Any]:
    """Get logging statistics and information."""
    stats = {
        "total_loggers": len(_loggers),
        "loggers": {},
        "setup_complete": _log_setup_done
    }
    
    for name, logger in _loggers.items():
        handler_info = []
        for handler in logger.handlers:
            handler_info.append({
                "type": type(handler).__name__,
                "level": logging.getLevelName(handler.level)
            })
        
        stats["loggers"][name] = {
            "level": logging.getLevelName(logger.level),
            "handlers": handler_info,
            "propagate": logger.propagate
        }
    
    return stats


def cleanup_loggers():
    """Clean up all loggers and handlers."""
    global _loggers, _log_setup_done
    
    for logger in _loggers.values():
        for handler in logger.handlers[:]:
            handler.close()
            logger.removeHandler(handler)
    
    _loggers.clear()
    _log_setup_done = False
