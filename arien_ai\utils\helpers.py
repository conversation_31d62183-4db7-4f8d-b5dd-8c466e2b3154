"""
Helper Utilities

Common utility functions used throughout the Arien AI system.
"""

import json
import re
import hashlib
import asyncio
import time
from typing import Any, Dict, List, Optional, Union, Callable
from pathlib import Path
from datetime import datetime, timezone
import json_repair

from .logger import get_logger


def format_output(data: Any, max_length: int = 5000, pretty: bool = True) -> str:
    """
    Format data for display with length limits and pretty printing.
    
    Args:
        data: Data to format
        max_length: Maximum output length
        pretty: Whether to use pretty formatting
        
    Returns:
        Formatted string
    """
    try:
        if data is None:
            return "None"
        
        if isinstance(data, str):
            output = data
        elif isinstance(data, (dict, list)):
            if pretty:
                output = json.dumps(data, indent=2, default=str, ensure_ascii=False)
            else:
                output = json.dumps(data, default=str, ensure_ascii=False)
        else:
            output = str(data)
        
        # Truncate if too long
        if len(output) > max_length:
            output = output[:max_length] + "\n... [Output truncated for brevity]"
        
        return output
        
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"Error formatting output: {e}")
        return f"[Error formatting output: {str(e)}]"


def parse_json_safely(json_str: str, repair: bool = True) -> Optional[Dict[str, Any]]:
    """
    Safely parse JSON string with optional repair functionality.
    
    Args:
        json_str: JSON string to parse
        repair: Whether to attempt repair of malformed JSON
        
    Returns:
        Parsed JSON data or None if parsing fails
    """
    logger = get_logger(__name__)
    
    if not json_str or not isinstance(json_str, str):
        return None
    
    try:
        # First try standard JSON parsing
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.debug(f"Standard JSON parsing failed: {e}")
        
        if repair:
            try:
                # Try to repair the JSON
                repaired = json_repair.repair_json(json_str)
                return json.loads(repaired)
            except Exception as repair_error:
                logger.debug(f"JSON repair failed: {repair_error}")
        
        # Try to extract JSON from text
        try:
            # Look for JSON-like patterns
            json_pattern = r'\{.*\}|\[.*\]'
            matches = re.findall(json_pattern, json_str, re.DOTALL)
            
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
                    
        except Exception as extract_error:
            logger.debug(f"JSON extraction failed: {extract_error}")
    
    return None


def validate_input(
    value: Any,
    expected_type: type,
    min_length: Optional[int] = None,
    max_length: Optional[int] = None,
    pattern: Optional[str] = None,
    allowed_values: Optional[List[Any]] = None
) -> tuple[bool, Optional[str]]:
    """
    Validate input value against specified criteria.
    
    Args:
        value: Value to validate
        expected_type: Expected type
        min_length: Minimum length (for strings/lists)
        max_length: Maximum length (for strings/lists)
        pattern: Regex pattern (for strings)
        allowed_values: List of allowed values
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Type validation
        if not isinstance(value, expected_type):
            return False, f"Expected {expected_type.__name__}, got {type(value).__name__}"
        
        # Length validation
        if hasattr(value, '__len__'):
            length = len(value)
            if min_length is not None and length < min_length:
                return False, f"Length {length} is less than minimum {min_length}"
            if max_length is not None and length > max_length:
                return False, f"Length {length} exceeds maximum {max_length}"
        
        # Pattern validation (for strings)
        if isinstance(value, str) and pattern:
            if not re.match(pattern, value):
                return False, f"Value does not match pattern: {pattern}"
        
        # Allowed values validation
        if allowed_values is not None and value not in allowed_values:
            return False, f"Value not in allowed list: {allowed_values}"
        
        return True, None
        
    except Exception as e:
        return False, f"Validation error: {str(e)}"


def generate_hash(data: Union[str, bytes, Dict, List], algorithm: str = "sha256") -> str:
    """
    Generate hash for data.
    
    Args:
        data: Data to hash
        algorithm: Hash algorithm (md5, sha1, sha256, sha512)
        
    Returns:
        Hex digest of hash
    """
    try:
        # Convert data to bytes
        if isinstance(data, str):
            data_bytes = data.encode('utf-8')
        elif isinstance(data, bytes):
            data_bytes = data
        elif isinstance(data, (dict, list)):
            data_bytes = json.dumps(data, sort_keys=True, default=str).encode('utf-8')
        else:
            data_bytes = str(data).encode('utf-8')
        
        # Generate hash
        hash_obj = hashlib.new(algorithm)
        hash_obj.update(data_bytes)
        return hash_obj.hexdigest()
        
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"Error generating hash: {e}")
        return ""


def safe_filename(filename: str, max_length: int = 255) -> str:
    """
    Create a safe filename by removing/replacing invalid characters.
    
    Args:
        filename: Original filename
        max_length: Maximum filename length
        
    Returns:
        Safe filename
    """
    # Remove invalid characters
    safe_chars = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove control characters
    safe_chars = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', safe_chars)
    
    # Trim whitespace and dots
    safe_chars = safe_chars.strip(' .')
    
    # Ensure not empty
    if not safe_chars:
        safe_chars = "unnamed_file"
    
    # Truncate if too long
    if len(safe_chars) > max_length:
        name, ext = Path(safe_chars).stem, Path(safe_chars).suffix
        max_name_length = max_length - len(ext)
        safe_chars = name[:max_name_length] + ext
    
    return safe_chars


def format_size(size_bytes: int) -> str:
    """
    Format byte size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def format_duration(seconds: float) -> str:
    """
    Format duration in human-readable format.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 1:
        return f"{seconds*1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.0f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        return f"{hours}h {minutes}m"


def get_timestamp(utc: bool = True, iso_format: bool = True) -> str:
    """
    Get current timestamp.
    
    Args:
        utc: Whether to use UTC time
        iso_format: Whether to use ISO format
        
    Returns:
        Timestamp string
    """
    if utc:
        dt = datetime.now(timezone.utc)
    else:
        dt = datetime.now()
    
    if iso_format:
        return dt.isoformat()
    else:
        return dt.strftime("%Y-%m-%d %H:%M:%S")


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split list into chunks of specified size.
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """
    Flatten nested dictionary.
    
    Args:
        d: Dictionary to flatten
        parent_key: Parent key prefix
        sep: Separator for nested keys
        
    Returns:
        Flattened dictionary
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def retry_on_exception(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """
    Decorator for retrying function calls on exceptions.
    
    Args:
        max_retries: Maximum number of retries
        delay: Initial delay between retries
        backoff: Backoff multiplier
        exceptions: Tuple of exceptions to catch
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        raise last_exception
            
            return None
        return wrapper
    return decorator


async def run_with_timeout(coro, timeout: float):
    """
    Run coroutine with timeout.
    
    Args:
        coro: Coroutine to run
        timeout: Timeout in seconds
        
    Returns:
        Coroutine result
        
    Raises:
        asyncio.TimeoutError: If timeout is exceeded
    """
    return await asyncio.wait_for(coro, timeout=timeout)


def is_valid_url(url: str) -> bool:
    """
    Check if string is a valid URL.
    
    Args:
        url: URL string to validate
        
    Returns:
        True if valid URL
    """
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    return url_pattern.match(url) is not None


def extract_urls(text: str) -> List[str]:
    """
    Extract URLs from text.
    
    Args:
        text: Text to search for URLs
        
    Returns:
        List of found URLs
    """
    url_pattern = re.compile(
        r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    )
    return url_pattern.findall(text)
