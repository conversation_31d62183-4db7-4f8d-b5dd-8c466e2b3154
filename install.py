#!/usr/bin/env python3
"""
Arien AI - Cross-Platform Installer/Updater/Uninstaller

This script provides complete installation, update, and uninstallation capabilities
for the Arien AI CLI system across Windows 11 WSL, macOS, and Linux.

Usage:
    python install.py install    # Install Arien AI
    python install.py update     # Update existing installation
    python install.py uninstall  # Uninstall Arien AI
    python install.py status     # Check installation status
"""

import os
import sys
import platform
import subprocess
import shutil
import json
import urllib.request
import zipfile
import tarfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse


class ArienAIInstaller:
    """Cross-platform installer for Arien AI CLI system."""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.is_wsl = self._detect_wsl()
        self.python_executable = sys.executable
        
        # Installation paths
        if self.system == "windows" or self.is_wsl:
            self.install_dir = Path.home() / ".arien-ai"
            self.bin_dir = Path.home() / ".local" / "bin"
        else:  # macOS and Linux
            self.install_dir = Path.home() / ".arien-ai"
            self.bin_dir = Path.home() / ".local" / "bin"
        
        # Ensure directories exist
        self.install_dir.mkdir(parents=True, exist_ok=True)
        self.bin_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration
        self.config_file = self.install_dir / "config.json"
        self.venv_dir = self.install_dir / "venv"
        self.source_dir = self.install_dir / "source"
        
        # Colors for output
        self.colors = {
            'red': '\033[91m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'magenta': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'bold': '\033[1m',
            'end': '\033[0m'
        }
    
    def _detect_wsl(self) -> bool:
        """Detect if running in Windows Subsystem for Linux."""
        try:
            with open('/proc/version', 'r') as f:
                return 'microsoft' in f.read().lower()
        except:
            return False
    
    def _colorize(self, text: str, color: str) -> str:
        """Add color to text output."""
        if color in self.colors:
            return f"{self.colors[color]}{text}{self.colors['end']}"
        return text
    
    def _print_header(self, text: str):
        """Print a formatted header."""
        print("\n" + "=" * 60)
        print(self._colorize(f" {text} ", 'bold'))
        print("=" * 60)
    
    def _print_step(self, step: str, status: str = ""):
        """Print a step with optional status."""
        if status:
            print(f"  {self._colorize('→', 'blue')} {step} {self._colorize(status, 'green')}")
        else:
            print(f"  {self._colorize('→', 'blue')} {step}")
    
    def _print_error(self, message: str):
        """Print an error message."""
        print(f"  {self._colorize('✗', 'red')} {self._colorize(message, 'red')}")
    
    def _print_success(self, message: str):
        """Print a success message."""
        print(f"  {self._colorize('✓', 'green')} {self._colorize(message, 'green')}")
    
    def _print_warning(self, message: str):
        """Print a warning message."""
        print(f"  {self._colorize('⚠', 'yellow')} {self._colorize(message, 'yellow')}")
    
    def _run_command(self, command: List[str], capture_output: bool = True, check: bool = True) -> subprocess.CompletedProcess:
        """Run a command and return the result."""
        try:
            result = subprocess.run(
                command,
                capture_output=capture_output,
                text=True,
                check=check
            )
            return result
        except subprocess.CalledProcessError as e:
            if not capture_output:
                raise
            self._print_error(f"Command failed: {' '.join(command)}")
            self._print_error(f"Error: {e.stderr}")
            raise
    
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        version = sys.version_info
        if version.major == 3 and version.minor >= 11:
            self._print_success(f"Python {version.major}.{version.minor}.{version.micro} is compatible")
            return True
        else:
            self._print_error(f"Python {version.major}.{version.minor}.{version.micro} is not compatible")
            self._print_error("Arien AI requires Python 3.11 or higher")
            return False
    
    def check_dependencies(self) -> bool:
        """Check if required system dependencies are available."""
        self._print_step("Checking system dependencies...")
        
        dependencies = []
        
        # Check for git
        try:
            self._run_command(['git', '--version'])
            self._print_success("Git is available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self._print_warning("Git not found - some features may be limited")
        
        # Check for curl or wget
        has_downloader = False
        for cmd in ['curl', 'wget']:
            try:
                self._run_command([cmd, '--version'])
                self._print_success(f"{cmd.capitalize()} is available")
                has_downloader = True
                break
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue
        
        if not has_downloader:
            self._print_warning("Neither curl nor wget found - using Python urllib")
        
        return True
    
    def create_virtual_environment(self) -> bool:
        """Create a virtual environment for Arien AI."""
        self._print_step("Creating virtual environment...")
        
        try:
            # Remove existing venv if it exists
            if self.venv_dir.exists():
                shutil.rmtree(self.venv_dir)
            
            # Create new virtual environment
            self._run_command([self.python_executable, '-m', 'venv', str(self.venv_dir)])
            self._print_success("Virtual environment created")
            
            # Get the python executable in the venv
            if self.system == "windows":
                self.venv_python = self.venv_dir / "Scripts" / "python.exe"
                self.venv_pip = self.venv_dir / "Scripts" / "pip.exe"
            else:
                self.venv_python = self.venv_dir / "bin" / "python"
                self.venv_pip = self.venv_dir / "bin" / "pip"
            
            # Upgrade pip
            self._run_command([str(self.venv_pip), 'install', '--upgrade', 'pip'])
            self._print_success("Pip upgraded")
            
            return True
            
        except Exception as e:
            self._print_error(f"Failed to create virtual environment: {e}")
            return False
    
    def install_package(self) -> bool:
        """Install the Arien AI package."""
        self._print_step("Installing Arien AI package...")
        
        try:
            # Copy source files to installation directory
            current_dir = Path(__file__).parent
            
            # Copy the entire arien_ai package
            if (current_dir / "arien_ai").exists():
                if self.source_dir.exists():
                    shutil.rmtree(self.source_dir)
                shutil.copytree(current_dir, self.source_dir, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                self._print_success("Source files copied")
            else:
                self._print_error("Source files not found. Make sure you're running from the project directory.")
                return False
            
            # Install dependencies
            requirements_file = self.source_dir / "requirements.txt"
            if requirements_file.exists():
                self._run_command([str(self.venv_pip), 'install', '-r', str(requirements_file)])
                self._print_success("Dependencies installed")
            
            # Install the package in development mode
            self._run_command([str(self.venv_pip), 'install', '-e', str(self.source_dir)])
            self._print_success("Arien AI package installed")
            
            return True
            
        except Exception as e:
            self._print_error(f"Failed to install package: {e}")
            return False
    
    def create_executable_scripts(self) -> bool:
        """Create executable scripts for the CLI."""
        self._print_step("Creating executable scripts...")
        
        try:
            # Create the main executable script
            if self.system == "windows":
                script_content = f"""@echo off
"{self.venv_python}" -m arien_ai.main %*
"""
                script_path = self.bin_dir / "arien.bat"
                alt_script_path = self.bin_dir / "arien-ai.bat"
            else:
                script_content = f"""#!/bin/bash
"{self.venv_python}" -m arien_ai.main "$@"
"""
                script_path = self.bin_dir / "arien"
                alt_script_path = self.bin_dir / "arien-ai"
            
            # Write the main script
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            # Make executable on Unix systems
            if self.system != "windows":
                script_path.chmod(0o755)
            
            # Create alternative name
            shutil.copy2(script_path, alt_script_path)
            
            self._print_success("Executable scripts created")
            return True
            
        except Exception as e:
            self._print_error(f"Failed to create executable scripts: {e}")
            return False
    
    def update_path(self) -> bool:
        """Update PATH environment variable."""
        self._print_step("Updating PATH...")
        
        try:
            # Check if bin directory is in PATH
            current_path = os.environ.get('PATH', '')
            bin_dir_str = str(self.bin_dir)
            
            if bin_dir_str not in current_path:
                # Add to shell configuration files
                shell_configs = []
                
                if self.system == "darwin":  # macOS
                    shell_configs = ['.zshrc', '.bash_profile', '.bashrc']
                elif self.system == "linux" or self.is_wsl:
                    shell_configs = ['.bashrc', '.zshrc', '.profile']
                
                path_line = f'export PATH="$PATH:{bin_dir_str}"'
                
                for config in shell_configs:
                    config_path = Path.home() / config
                    if config_path.exists() or config == '.bashrc':
                        try:
                            with open(config_path, 'r') as f:
                                content = f.read()
                            
                            if path_line not in content:
                                with open(config_path, 'a') as f:
                                    f.write(f'\n# Added by Arien AI installer\n{path_line}\n')
                                self._print_success(f"Updated {config}")
                        except Exception as e:
                            self._print_warning(f"Could not update {config}: {e}")
                
                self._print_warning(f"Please restart your terminal or run: source ~/.bashrc")
            else:
                self._print_success("PATH already contains bin directory")
            
            return True
            
        except Exception as e:
            self._print_error(f"Failed to update PATH: {e}")
            return False
    
    def save_installation_info(self) -> bool:
        """Save installation information."""
        self._print_step("Saving installation information...")
        
        try:
            install_info = {
                "version": "1.0.0",
                "install_date": str(Path(__file__).stat().st_mtime),
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "system": self.system,
                "is_wsl": self.is_wsl,
                "install_dir": str(self.install_dir),
                "bin_dir": str(self.bin_dir),
                "venv_dir": str(self.venv_dir)
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(install_info, f, indent=2)
            
            self._print_success("Installation information saved")
            return True
            
        except Exception as e:
            self._print_error(f"Failed to save installation info: {e}")
            return False
    
    def install(self) -> bool:
        """Complete installation process."""
        self._print_header("INSTALLING ARIEN AI")
        
        # Check prerequisites
        if not self.check_python_version():
            return False
        
        if not self.check_dependencies():
            return False
        
        # Installation steps
        steps = [
            self.create_virtual_environment,
            self.install_package,
            self.create_executable_scripts,
            self.update_path,
            self.save_installation_info
        ]
        
        for step in steps:
            if not step():
                self._print_error("Installation failed")
                return False
        
        self._print_header("INSTALLATION COMPLETE")
        self._print_success("Arien AI has been successfully installed!")
        self._print_step("You can now use 'arien' or 'arien-ai' commands")
        self._print_step("Run 'arien --help' to get started")
        
        return True
    
    def update(self) -> bool:
        """Update existing installation."""
        self._print_header("UPDATING ARIEN AI")
        
        if not self.is_installed():
            self._print_error("Arien AI is not installed. Use 'install' command first.")
            return False
        
        # Update is essentially a reinstall
        return self.install()
    
    def uninstall(self) -> bool:
        """Uninstall Arien AI."""
        self._print_header("UNINSTALLING ARIEN AI")
        
        try:
            # Remove installation directory
            if self.install_dir.exists():
                shutil.rmtree(self.install_dir)
                self._print_success("Installation directory removed")
            
            # Remove executable scripts
            for script_name in ['arien', 'arien-ai', 'arien.bat', 'arien-ai.bat']:
                script_path = self.bin_dir / script_name
                if script_path.exists():
                    script_path.unlink()
                    self._print_success(f"Removed {script_name}")
            
            self._print_header("UNINSTALLATION COMPLETE")
            self._print_success("Arien AI has been successfully uninstalled!")
            self._print_warning("You may need to manually remove PATH entries from shell config files")
            
            return True
            
        except Exception as e:
            self._print_error(f"Uninstallation failed: {e}")
            return False
    
    def is_installed(self) -> bool:
        """Check if Arien AI is installed."""
        return self.config_file.exists() and self.venv_dir.exists()
    
    def status(self) -> bool:
        """Show installation status."""
        self._print_header("ARIEN AI STATUS")
        
        if self.is_installed():
            try:
                with open(self.config_file, 'r') as f:
                    info = json.load(f)
                
                self._print_success("Arien AI is installed")
                self._print_step(f"Version: {info.get('version', 'Unknown')}")
                self._print_step(f"Install Directory: {info.get('install_dir')}")
                self._print_step(f"Python Version: {info.get('python_version')}")
                self._print_step(f"System: {info.get('system')}")
                
                # Check if executables exist
                script_name = "arien.bat" if self.system == "windows" else "arien"
                script_path = self.bin_dir / script_name
                if script_path.exists():
                    self._print_success("Executable scripts are available")
                else:
                    self._print_warning("Executable scripts not found")
                
                return True
                
            except Exception as e:
                self._print_error(f"Error reading installation info: {e}")
                return False
        else:
            self._print_error("Arien AI is not installed")
            return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Arien AI Cross-Platform Installer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python install.py install    # Install Arien AI
  python install.py update     # Update existing installation
  python install.py uninstall  # Uninstall Arien AI
  python install.py status     # Check installation status
        """
    )
    
    parser.add_argument(
        'action',
        choices=['install', 'update', 'uninstall', 'status'],
        help='Action to perform'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='Force action without confirmation'
    )
    
    args = parser.parse_args()
    
    installer = ArienAIInstaller()
    
    # Confirmation for destructive actions
    if args.action in ['uninstall'] and not args.force:
        response = input(f"Are you sure you want to {args.action} Arien AI? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("Operation cancelled.")
            return
    
    # Execute the requested action
    if args.action == 'install':
        success = installer.install()
    elif args.action == 'update':
        success = installer.update()
    elif args.action == 'uninstall':
        success = installer.uninstall()
    elif args.action == 'status':
        success = installer.status()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
