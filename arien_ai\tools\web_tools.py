"""
Web Tools

Comprehensive web interaction capabilities for the Arien AI system.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Union
from urllib.parse import urljoin, urlparse, parse_qs
import httpx
from bs4 import BeautifulSoup
import re

from .base_tool import BaseTool, ToolResult
from ..config.settings import get_settings
from ..utils.logger import get_logger
from ..utils.helpers import is_valid_url, extract_urls, format_size


class WebTools(BaseTool):
    """
    Web interaction tools providing comprehensive web scraping and API capabilities.
    
    Features:
    - HTTP requests (GET, POST, PUT, DELETE)
    - Web scraping with BeautifulSoup
    - Content extraction and analysis
    - API interaction and JSON handling
    - URL validation and processing
    """
    
    def __init__(self, verbose: bool = False):
        super().__init__("web_tools", "Web scraping and HTTP request capabilities", verbose)
        self.settings = get_settings()
        self.timeout = self.settings.web_tools_timeout
        self.max_content_length = self.settings.web_tools_max_content_length
        self.user_agent = self.settings.web_tools_user_agent
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            timeout=self.timeout,
            headers={"User-Agent": self.user_agent},
            follow_redirects=True
        )
    
    async def fetch_url(
        self,
        url: str,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Union[str, Dict[str, Any]]] = None,
        json_data: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """
        Fetch content from URL with various HTTP methods.
        
        Args:
            url: URL to fetch
            method: HTTP method (GET, POST, PUT, DELETE)
            headers: Additional headers
            params: URL parameters
            data: Form data or raw data
            json_data: JSON data for request body
        """
        try:
            # Validate URL
            if not is_valid_url(url):
                return ToolResult(success=False, error=f"Invalid URL: {url}")
            
            # Prepare request
            request_headers = headers or {}
            
            # Make request
            response = await self.client.request(
                method=method.upper(),
                url=url,
                headers=request_headers,
                params=params,
                data=data,
                json=json_data
            )
            
            # Check content length
            content_length = len(response.content)
            if content_length > self.max_content_length:
                return ToolResult(
                    success=False,
                    error=f"Content too large: {format_size(content_length)} > {format_size(self.max_content_length)}"
                )
            
            # Process response
            result_data = {
                "url": str(response.url),
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_type": response.headers.get("content-type", ""),
                "content_length": content_length,
                "encoding": response.encoding
            }
            
            # Handle different content types
            content_type = response.headers.get("content-type", "").lower()
            
            if "application/json" in content_type:
                try:
                    result_data["content"] = response.json()
                    result_data["content_type_parsed"] = "json"
                except Exception as e:
                    result_data["content"] = response.text
                    result_data["json_parse_error"] = str(e)
            elif "text/" in content_type or "application/xml" in content_type:
                result_data["content"] = response.text
                result_data["content_type_parsed"] = "text"
            else:
                # Binary content
                result_data["content"] = f"<Binary content: {format_size(content_length)}>"
                result_data["content_type_parsed"] = "binary"
                result_data["binary_data_available"] = True
            
            # Add request info
            metadata = {
                "request": {
                    "method": method.upper(),
                    "url": url,
                    "headers": request_headers,
                    "params": params
                },
                "response": {
                    "status_code": response.status_code,
                    "content_length": content_length,
                    "content_type": content_type
                }
            }
            
            return ToolResult(
                success=True,
                data=result_data,
                metadata=metadata
            )
            
        except httpx.TimeoutException:
            return ToolResult(success=False, error=f"Request timeout after {self.timeout}s")
        except httpx.RequestError as e:
            return ToolResult(success=False, error=f"Request error: {str(e)}")
        except Exception as e:
            return ToolResult(success=False, error=f"Error fetching URL: {str(e)}")
    
    async def scrape_content(
        self,
        url: str,
        selector: Optional[str] = None,
        extract_links: bool = False,
        extract_images: bool = False,
        extract_text: bool = True
    ) -> ToolResult:
        """
        Scrape and extract content from web page.
        
        Args:
            url: URL to scrape
            selector: CSS selector for specific elements
            extract_links: Extract all links from page
            extract_images: Extract all images from page
            extract_text: Extract text content
        """
        try:
            # Fetch the page
            fetch_result = await self.fetch_url(url)
            if not fetch_result.success:
                return fetch_result
            
            response_data = fetch_result.data
            
            # Check if we got HTML content
            if "text/html" not in response_data.get("content_type", ""):
                return ToolResult(
                    success=False,
                    error=f"URL does not return HTML content: {response_data.get('content_type')}"
                )
            
            # Parse HTML
            soup = BeautifulSoup(response_data["content"], 'html.parser')
            
            result = {
                "url": url,
                "title": soup.title.string.strip() if soup.title else None,
                "meta_description": None,
                "content": {}
            }
            
            # Extract meta description
            meta_desc = soup.find("meta", attrs={"name": "description"})
            if meta_desc:
                result["meta_description"] = meta_desc.get("content")
            
            # Extract specific elements if selector provided
            if selector:
                elements = soup.select(selector)
                result["content"]["selected_elements"] = []
                
                for elem in elements:
                    elem_data = {
                        "tag": elem.name,
                        "text": elem.get_text(strip=True),
                        "attributes": dict(elem.attrs)
                    }
                    result["content"]["selected_elements"].append(elem_data)
            
            # Extract text content
            if extract_text:
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                text = soup.get_text()
                # Clean up text
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                text = ' '.join(chunk for chunk in chunks if chunk)
                
                result["content"]["text"] = text
                result["content"]["text_length"] = len(text)
                result["content"]["word_count"] = len(text.split())
            
            # Extract links
            if extract_links:
                links = []
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    # Convert relative URLs to absolute
                    absolute_url = urljoin(url, href)
                    
                    link_data = {
                        "url": absolute_url,
                        "text": link.get_text(strip=True),
                        "title": link.get("title"),
                        "rel": link.get("rel")
                    }
                    links.append(link_data)
                
                result["content"]["links"] = links
                result["content"]["link_count"] = len(links)
            
            # Extract images
            if extract_images:
                images = []
                for img in soup.find_all('img', src=True):
                    src = img['src']
                    # Convert relative URLs to absolute
                    absolute_url = urljoin(url, src)
                    
                    img_data = {
                        "url": absolute_url,
                        "alt": img.get("alt"),
                        "title": img.get("title"),
                        "width": img.get("width"),
                        "height": img.get("height")
                    }
                    images.append(img_data)
                
                result["content"]["images"] = images
                result["content"]["image_count"] = len(images)
            
            metadata = {
                "scraping_options": {
                    "selector": selector,
                    "extract_links": extract_links,
                    "extract_images": extract_images,
                    "extract_text": extract_text
                },
                "page_info": {
                    "status_code": response_data["status_code"],
                    "content_length": response_data["content_length"]
                }
            }
            
            return ToolResult(
                success=True,
                data=result,
                metadata=metadata
            )
            
        except Exception as e:
            return ToolResult(success=False, error=f"Error scraping content: {str(e)}")
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute web operation based on method parameter."""
        method = kwargs.get('method', 'fetch_url')
        
        if method == 'fetch_url':
            return await self.fetch_url(
                kwargs.get('url'),
                kwargs.get('http_method', 'GET'),
                kwargs.get('headers'),
                kwargs.get('params'),
                kwargs.get('data'),
                kwargs.get('json_data')
            )
        elif method == 'scrape_content':
            return await self.scrape_content(
                kwargs.get('url'),
                kwargs.get('selector'),
                kwargs.get('extract_links', False),
                kwargs.get('extract_images', False),
                kwargs.get('extract_text', True)
            )
        else:
            return ToolResult(success=False, error=f"Unknown method: {method}")
    
    def get_schema(self) -> Dict[str, Any]:
        """Get schema for web tools."""
        return {
            "parameters": {
                "type": "object",
                "properties": {
                    "method": {
                        "type": "string",
                        "enum": ["fetch_url", "scrape_content"],
                        "description": "Web operation to perform"
                    },
                    "url": {
                        "type": "string",
                        "description": "URL to process"
                    },
                    "http_method": {
                        "type": "string",
                        "enum": ["GET", "POST", "PUT", "DELETE"],
                        "description": "HTTP method for fetch_url",
                        "default": "GET"
                    },
                    "headers": {
                        "type": "object",
                        "description": "Additional HTTP headers"
                    },
                    "params": {
                        "type": "object",
                        "description": "URL parameters"
                    },
                    "data": {
                        "type": "string",
                        "description": "Request body data"
                    },
                    "json_data": {
                        "type": "object",
                        "description": "JSON data for request body"
                    },
                    "selector": {
                        "type": "string",
                        "description": "CSS selector for scraping specific elements"
                    },
                    "extract_links": {
                        "type": "boolean",
                        "description": "Extract all links from page",
                        "default": False
                    },
                    "extract_images": {
                        "type": "boolean",
                        "description": "Extract all images from page",
                        "default": False
                    },
                    "extract_text": {
                        "type": "boolean",
                        "description": "Extract text content",
                        "default": True
                    }
                },
                "required": ["method", "url"]
            }
        }
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
