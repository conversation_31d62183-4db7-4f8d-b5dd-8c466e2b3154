"""
Base Tool Interface

Defines the interface and common functionality for all tools in the system.
"""

import json
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from datetime import datetime

from ..utils.logger import get_logger


@dataclass
class ToolResult:
    """Standardized result format for all tool executions."""
    
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    tool_name: str = ""
    tool_call_id: Optional[str] = None
    timestamp: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=2, default=str)


class BaseTool(ABC):
    """
    Base class for all tools in the Arien AI system.
    
    All tools must inherit from this class and implement the required methods.
    This ensures consistent behavior and error handling across all tools.
    """
    
    def __init__(self, name: str, description: str, verbose: bool = False):
        self.name = name
        self.description = description
        self.verbose = verbose
        self.logger = get_logger(f"tool.{name}")
        self.execution_count = 0
        self.success_count = 0
        self.error_count = 0
        self.total_execution_time = 0.0
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """
        Return the JSON schema for this tool's parameters.
        
        This schema is used by the LLM to understand how to call the tool.
        Must follow OpenAI function calling format.
        """
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute the tool with the given parameters.
        
        Args:
            **kwargs: Tool-specific parameters
            
        Returns:
            ToolResult: Standardized result object
        """
        pass
    
    async def safe_execute(self, tool_call_id: Optional[str] = None, **kwargs) -> ToolResult:
        """
        Safely execute the tool with error handling and metrics tracking.
        
        Args:
            tool_call_id: Unique identifier for this tool call
            **kwargs: Tool-specific parameters
            
        Returns:
            ToolResult: Result with success/error information
        """
        start_time = time.time()
        self.execution_count += 1
        
        try:
            if self.verbose:
                self.logger.info(f"Executing {self.name} with parameters: {kwargs}")
            
            # Validate parameters
            validation_error = self.validate_parameters(kwargs)
            if validation_error:
                raise ValueError(f"Parameter validation failed: {validation_error}")
            
            # Execute the tool
            result = await self.execute(**kwargs)
            
            # Update result metadata
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            result.tool_name = self.name
            result.tool_call_id = tool_call_id
            
            # Update metrics
            self.total_execution_time += execution_time
            if result.success:
                self.success_count += 1
            else:
                self.error_count += 1
            
            if self.verbose:
                self.logger.info(f"Tool {self.name} executed successfully in {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.error_count += 1
            self.total_execution_time += execution_time
            
            error_msg = str(e)
            self.logger.error(f"Tool {self.name} execution failed: {error_msg}")
            
            return ToolResult(
                success=False,
                error=error_msg,
                execution_time=execution_time,
                tool_name=self.name,
                tool_call_id=tool_call_id,
                metadata={"exception_type": type(e).__name__}
            )
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> Optional[str]:
        """
        Validate tool parameters against the schema.
        
        Args:
            parameters: Parameters to validate
            
        Returns:
            Error message if validation fails, None if valid
        """
        try:
            schema = self.get_schema()
            required_params = schema.get("parameters", {}).get("required", [])
            
            # Check required parameters
            for param in required_params:
                if param not in parameters:
                    return f"Missing required parameter: {param}"
            
            # Additional custom validation can be implemented in subclasses
            return self.custom_validation(parameters)
            
        except Exception as e:
            return f"Schema validation error: {str(e)}"
    
    def custom_validation(self, parameters: Dict[str, Any]) -> Optional[str]:
        """
        Override this method for custom parameter validation.
        
        Args:
            parameters: Parameters to validate
            
        Returns:
            Error message if validation fails, None if valid
        """
        return None
    
    def get_openai_function_schema(self) -> Dict[str, Any]:
        """
        Get the tool schema in OpenAI function calling format.
        
        Returns:
            Dictionary in OpenAI function format
        """
        schema = self.get_schema()
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": schema.get("parameters", {})
            }
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get execution statistics for this tool."""
        success_rate = (self.success_count / max(1, self.execution_count)) * 100
        avg_execution_time = self.total_execution_time / max(1, self.execution_count)
        
        return {
            "name": self.name,
            "executions": self.execution_count,
            "successes": self.success_count,
            "errors": self.error_count,
            "success_rate": f"{success_rate:.1f}%",
            "total_time": f"{self.total_execution_time:.2f}s",
            "avg_time": f"{avg_execution_time:.2f}s"
        }
    
    def reset_statistics(self):
        """Reset execution statistics."""
        self.execution_count = 0
        self.success_count = 0
        self.error_count = 0
        self.total_execution_time = 0.0
        
        if self.verbose:
            self.logger.info(f"Statistics reset for tool: {self.name}")
    
    def __str__(self) -> str:
        return f"Tool({self.name}): {self.description}"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}', executions={self.execution_count})>"
