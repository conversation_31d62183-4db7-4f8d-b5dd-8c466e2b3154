Metadata-Version: 2.4
Name: arien-ai
Version: 1.0.0
Summary: Powerful Local Agentic CLI Terminal System with LLM Function Tools
Author-email: Arien AI <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/arien-ai/arien-ai
Project-URL: Repository, https://github.com/arien-ai/arien-ai
Project-URL: Documentation, https://github.com/arien-ai/arien-ai/docs
Project-URL: Bug Tracker, https://github.com/arien-ai/arien-ai/issues
Keywords: ai,cli,agent,llm,tools,automation
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.11
Description-Content-Type: text/markdown
Requires-Dist: typer[all]>=0.12.0
Requires-Dist: rich>=13.7.0
Requires-Dist: httpx>=0.27.0
Requires-Dist: asyncio-mqtt>=0.16.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: aiofiles>=23.2.0
Requires-Dist: beautifulsoup4>=4.12.0
Requires-Dist: lxml>=4.9.0
Requires-Dist: requests>=2.31.0
Requires-Dist: openai>=1.50.0
Requires-Dist: ollama>=0.5.0
Requires-Dist: psutil>=5.9.0
Requires-Dist: pathlib>=1.0.0
Requires-Dist: json-repair>=0.25.0
Requires-Dist: tenacity>=8.2.0
Requires-Dist: colorama>=0.4.6
Requires-Dist: prompt-toolkit>=3.0.43
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.5.0; extra == "dev"

# 🤖 Arien AI - Powerful Local Agentic CLI Terminal System

A sophisticated local agentic CLI terminal system with advanced LLM function calling capabilities, intelligent retry logic, and comprehensive tool execution. Built with the latest Python technologies and designed for maximum reliability and performance.

## ✨ Features

### 🧠 Core Capabilities
- **Advanced LLM Integration**: Support for DeepSeek (deepseek-chat, deepseek-reasoner) and Ollama
- **Intelligent Tool Execution**: Dynamic tool loading with comprehensive error handling
- **Never Give Up Logic**: Persistent retry mechanisms with exponential backoff
- **Rich CLI Interface**: Beautiful terminal UI with Rich library integration
- **Cross-Platform Support**: Windows 11 WSL, macOS, and Linux compatibility

### 🛠️ Tool System
- **File Tools**: Complete file system operations with safety checks
- **Web Tools**: HTTP requests, web scraping, and content extraction
- **System Tools**: Command execution and system monitoring
- **Code Tools**: Multi-language code analysis and generation

### 🔄 Retry & Recovery
- **Intelligent Retry Logic**: Multiple retry strategies with backoff
- **Error Recovery**: Automatic alternative approach generation
- **Context Preservation**: Maintains conversation state across retries
- **Resource Management**: Efficient resource usage and monitoring

## 🚀 Quick Start

### Prerequisites
- Python 3.11 or higher
- Git (optional, for enhanced features)
- Internet connection (for LLM providers)

### Installation

1. **Clone or download the repository**:
```bash
git clone <repository-url>
cd arien-ai
```

2. **Run the installer**:
```bash
python install.py install
```

3. **Configure your LLM providers**:
```bash
# For DeepSeek
export DEEPSEEK_API_KEY="your-api-key-here"

# For Ollama (if using local models)
export OLLAMA_HOST="http://localhost:11434"
```

4. **Start using Arien AI**:
```bash
arien chat "Hello, help me analyze my Python project"
```

## 📖 Usage

### Basic Commands

```bash
# Start interactive chat
arien chat

# Chat with specific provider and model
arien chat --provider deepseek --model deepseek-chat

# One-shot command
arien chat "List all Python files in the current directory"

# Enable verbose mode
arien chat --verbose "Analyze system performance"

# Show configuration
arien config --show

# Check version
arien version
```

### Configuration

Arien AI can be configured through environment variables or configuration files:

```bash
# Set configuration values
arien config --set deepseek_api_key --value "your-key"
arien config --set ollama_host --value "http://localhost:11434"
```

### Environment Variables

```bash
# LLM Provider Settings
export DEEPSEEK_API_KEY="your-deepseek-api-key"
export DEEPSEEK_DEFAULT_MODEL="deepseek-chat"
export OLLAMA_HOST="http://localhost:11434"
export OLLAMA_DEFAULT_MODEL="llama3.2"

# Tool Settings
export ARIEN_TOOLS_ENABLED=true
export ARIEN_FILE_TOOLS_MAX_SIZE=10485760  # 10MB
export ARIEN_WEB_TOOLS_TIMEOUT=30

# Retry Settings
export ARIEN_RETRY_MAX_ATTEMPTS=3
export ARIEN_RETRY_BASE_DELAY=1.0

# Logging
export ARIEN_LOG_LEVEL=INFO
export ARIEN_VERBOSE=false
```

## 🏗️ Architecture

### Core Components

```
arien_ai/
├── main.py                 # CLI entry point
├── core/
│   ├── agent.py           # Main agent orchestrator
│   ├── llm_providers.py   # LLM provider implementations
│   ├── tool_executor.py   # Tool execution engine
│   └── retry_logic.py     # Retry and recovery system
├── tools/
│   ├── base_tool.py       # Base tool interface
│   ├── file_tools.py      # File system operations
│   ├── web_tools.py       # Web interaction tools
│   ├── system_tools.py    # System administration
│   └── code_tools.py      # Code analysis tools
├── config/
│   ├── settings.py        # Configuration management
│   └── system_prompt.py   # System prompt definitions
└── utils/
    ├── logger.py          # Logging utilities
    └── helpers.py         # Helper functions
```

### Tool System

The tool system is designed for maximum extensibility:

```python
from arien_ai.tools.base_tool import BaseTool, ToolResult

class CustomTool(BaseTool):
    def __init__(self):
        super().__init__("custom_tool", "My custom tool")
    
    async def execute(self, **kwargs) -> ToolResult:
        # Your tool logic here
        return ToolResult(success=True, data="Result")
    
    def get_schema(self) -> Dict[str, Any]:
        return {
            "parameters": {
                "type": "object",
                "properties": {
                    "param1": {"type": "string", "description": "Parameter 1"}
                },
                "required": ["param1"]
            }
        }
```

## 🔧 Advanced Configuration

### Custom System Prompts

You can customize the system prompt by modifying `prompts/system_prompt.md` or through the configuration system:

```python
from arien_ai.config.system_prompt import get_system_prompt

prompt = get_system_prompt()
prompt.update_section("identity", "Your custom identity...")
```

### Provider Configuration

#### DeepSeek Setup
1. Get API key from [DeepSeek](https://platform.deepseek.com/)
2. Set environment variable: `export DEEPSEEK_API_KEY="your-key"`
3. Choose model: `deepseek-chat` or `deepseek-reasoner`

#### Ollama Setup
1. Install [Ollama](https://ollama.ai/)
2. Pull a model: `ollama pull llama3.2`
3. Start Ollama service: `ollama serve`
4. Configure host: `export OLLAMA_HOST="http://localhost:11434"`

## 🛡️ Security & Safety

### Built-in Safety Features
- **Path Validation**: Prevents directory traversal attacks
- **Command Filtering**: Blocks dangerous system commands by default
- **Size Limits**: Configurable limits for file and content sizes
- **Permission Checks**: Respects file system permissions
- **API Key Masking**: Automatically masks sensitive data in logs

### Security Best Practices
- Store API keys in environment variables, not code
- Use the `secure_mode` setting for enhanced security
- Regularly update dependencies
- Monitor system resource usage
- Review tool execution logs

## 📊 Monitoring & Debugging

### Logging
```bash
# Enable debug logging
export ARIEN_LOG_LEVEL=DEBUG

# Log to file
export ARIEN_LOG_FILE="/path/to/logfile.log"

# Verbose mode
arien chat --verbose "Your command"
```

### Statistics
```bash
# Check agent status
arien chat
> status

# View tool statistics
arien chat
> help
```

## 🔄 Updates & Maintenance

### Updating Arien AI
```bash
# Update to latest version
python install.py update

# Check current status
python install.py status
```

### Uninstalling
```bash
# Complete uninstallation
python install.py uninstall
```

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd arien-ai

# Create development environment
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Install in development mode
pip install -e .
pip install -r requirements.txt
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Common Issues

**Issue**: Command not found after installation
**Solution**: Restart your terminal or run `source ~/.bashrc`

**Issue**: Permission denied errors
**Solution**: Check file permissions and ensure proper installation

**Issue**: LLM provider connection errors
**Solution**: Verify API keys and network connectivity

### Getting Help

1. Check the documentation
2. Review the system logs
3. Use verbose mode for debugging
4. Check GitHub issues
5. Contact support

## 🎯 Roadmap

- [ ] Additional LLM provider support
- [ ] Plugin system for custom tools
- [ ] Web interface
- [ ] Docker containerization
- [ ] Cloud deployment options
- [ ] Advanced analytics and reporting

## 🙏 Acknowledgments

- Built with [Typer](https://typer.tiangolo.com/) for CLI interface
- [Rich](https://rich.readthedocs.io/) for beautiful terminal output
- [httpx](https://www.python-httpx.org/) for async HTTP requests
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) for web scraping
- [Pydantic](https://pydantic-docs.helpmanual.io/) for data validation

---

**Made with ❤️ by the Arien AI Team**
