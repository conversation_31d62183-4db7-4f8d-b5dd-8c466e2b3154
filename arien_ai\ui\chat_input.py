"""
Enhanced Chat Input Component

Provides advanced chat input functionality with interrupt support and slash commands.
"""

import sys
import time
import threading
from typing import Optional, Dict, Any, Callable
from rich.console import Console
from rich.prompt import Prompt
from rich.text import Text
from .base_component import BaseUIComponent
from .slash_commands import SlashCommandsComponent


class ChatInputComponent(BaseUIComponent):
    """
    Enhanced chat input component with advanced features.
    
    Features:
    - Double ESC interrupt functionality
    - Slash commands integration
    - Input history
    - Real-time input processing
    - Interrupt handling during AI processing
    """
    
    def __init__(self, console: Optional[Console] = None):
        super().__init__("chat_input", console)
        
        self.slash_commands = SlashCommandsComponent(console)
        self.input_history: list = []
        self.history_index = -1
        self.interrupt_requested = False
        self.processing = False
        
        # Interrupt detection
        self._last_esc_time = 0
        self._esc_threshold = 0.5  # 500ms for double ESC
        
    def render(self, prompt_text: str = "You", prompt_style: str = "bold blue") -> Dict[str, Any]:
        """
        Render the chat input interface.
        
        Args:
            prompt_text: Text to show in the prompt
            prompt_style: Rich style for the prompt
            
        Returns:
            Dictionary with input result and metadata
        """
        self.interrupt_requested = False
        
        try:
            # Show prompt with enhanced styling
            styled_prompt = f"[{prompt_style}]{prompt_text}[/{prompt_style}]"
            
            # Get user input
            user_input = self._get_enhanced_input(styled_prompt)
            
            if user_input is None:
                return {"action": "interrupted"}
            
            # Handle empty input
            if not user_input.strip():
                return {"action": "empty"}
            
            # Handle slash commands
            if user_input.startswith("/"):
                try:
                    command_result = self.slash_commands.render(user_input)
                    if command_result:
                        return command_result
                    else:
                        return {"action": "cancelled"}
                except Exception as e:
                    self.log_error(f"Error processing slash command: {e}")
                    self.console.print(f"[red]Error processing command: {e}[/red]")
                    return {"action": "error", "error": str(e)}
            
            # Handle special commands
            if user_input.lower() in ["exit", "quit", "bye"]:
                return {"action": "exit"}
            elif user_input.lower() == "help":
                return {"action": "show_help"}
            elif user_input.lower() == "clear":
                return {"action": "clear_screen"}
            elif user_input.lower() == "status":
                return {"action": "show_status"}
            
            # Add to history
            self._add_to_history(user_input)
            
            # Return user message
            return {
                "action": "user_message",
                "message": user_input,
                "timestamp": time.time()
            }
            
        except KeyboardInterrupt:
            return self._handle_keyboard_interrupt()
        except Exception as e:
            self.log_error(f"Error in chat input: {e}")
            return {"action": "error", "error": str(e)}
    
    def _get_enhanced_input(self, prompt: str) -> Optional[str]:
        """Get user input with enhanced features."""
        try:
            # Use Rich prompt for now (can be enhanced with custom input handling)
            user_input = Prompt.ask(prompt)
            return user_input
            
        except KeyboardInterrupt:
            # Handle Ctrl+C
            current_time = time.time()
            if current_time - self._last_esc_time < self._esc_threshold:
                # Double interrupt - force exit
                self.log_debug("Double interrupt detected")
                return None
            else:
                self._last_esc_time = current_time
                self.console.print("\n[yellow]Press Ctrl+C again quickly to exit, or continue typing...[/yellow]")
                return self._get_enhanced_input(prompt)
    
    def _add_to_history(self, input_text: str) -> None:
        """Add input to history."""
        if input_text.strip() and (not self.input_history or self.input_history[-1] != input_text):
            self.input_history.append(input_text)
            
            # Limit history size
            max_history = self.settings.ui_chat_history_size
            if len(self.input_history) > max_history:
                self.input_history = self.input_history[-max_history:]
        
        self.history_index = len(self.input_history)
    
    def _handle_keyboard_interrupt(self) -> Dict[str, Any]:
        """Handle keyboard interrupt (Ctrl+C)."""
        if self.processing:
            self.interrupt_requested = True
            self.console.print("\n[yellow]Interrupt requested. Stopping current operation...[/yellow]")
            return {"action": "interrupt_processing"}
        else:
            self.console.print("\n[yellow]Use 'exit' command or double Ctrl+C to quit[/yellow]")
            return {"action": "continue"}
    
    def set_processing(self, processing: bool) -> None:
        """Set processing state for interrupt handling."""
        self.processing = processing
        if not processing:
            self.interrupt_requested = False
    
    def is_interrupt_requested(self) -> bool:
        """Check if interrupt was requested."""
        return self.interrupt_requested
    
    def get_history(self) -> list:
        """Get input history."""
        return self.input_history.copy()
    
    def clear_history(self) -> None:
        """Clear input history."""
        self.input_history.clear()
        self.history_index = -1
        self.log_debug("Input history cleared")


class InterruptibleProcessor:
    """
    Helper class for handling interruptible processing.
    
    Allows long-running operations to be interrupted by user input.
    """
    
    def __init__(self, chat_input: ChatInputComponent):
        self.chat_input = chat_input
        self._stop_event = threading.Event()
    
    def start_processing(self) -> None:
        """Start processing mode."""
        self.chat_input.set_processing(True)
        self._stop_event.clear()
    
    def stop_processing(self) -> None:
        """Stop processing mode."""
        self.chat_input.set_processing(False)
        self._stop_event.set()
    
    def is_interrupted(self) -> bool:
        """Check if processing should be interrupted."""
        return self.chat_input.is_interrupt_requested() or self._stop_event.is_set()
    
    def __enter__(self):
        """Context manager entry."""
        self.start_processing()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_processing()


def create_chat_input(console: Optional[Console] = None) -> ChatInputComponent:
    """
    Create a chat input component.
    
    Args:
        console: Rich console instance
        
    Returns:
        Configured ChatInputComponent
    """
    return ChatInputComponent(console)
